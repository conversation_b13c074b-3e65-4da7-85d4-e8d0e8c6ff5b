# Duplicate API Calls Fix: Dashboard Route

## 🔍 **Issue Identified**

The Dashboard route was making **duplicate API calls** to:
- `GET /ingestion/v1/things`
- `GET /data/things/latestEventTime?lookbackInDays=30`

## 🕵️ **Root Cause Analysis**

### **Two Components Using useDeviceState with Different Parameters:**

1. **`PageContent` component** (Dashboard.tsx:106):
   ```typescript
   const { devices, isLoading } = useDeviceState(lookbackInDays); // Dynamic lookback
   ```
   - Query Key: `["device-state", 7]` (or whatever the time range is)

2. **`DeviceList` component** (components.tsx:386):
   ```typescript
   const { devices, isLoading } = useDeviceState(); // Default lookback (30 days)
   ```
   - Query Key: `["device-state", 30]` (default)

### **Why This Caused Duplicates:**

React Query uses the **query key** for caching. Since the components used different lookback periods:
- `["device-state", 7]` ≠ `["device-state", 30]`
- React Query treated these as **separate queries**
- Both components triggered their own API calls

## 🛠️ **Solution Implemented**

### **Synchronized Lookback Periods**

Made both components use the **same lookback period** from the time range selector:

#### **1. Updated DeviceList Component:**
```typescript
// BEFORE: Fixed default parameter
export const DeviceList = () => {
  const { devices, isLoading } = useDeviceState(); // Default 30 days
}

// AFTER: Accepts lookback parameter
export const DeviceList = ({ lookbackInDays = 30 }: { lookbackInDays?: number }) => {
  const { devices, isLoading } = useDeviceState(lookbackInDays); // Uses passed parameter
}
```

#### **2. Updated Dashboard to Pass Parameter:**
```typescript
// BEFORE: No parameter passed
<DeviceListWithScrollIndicators />

// AFTER: Pass the same lookback period
<DeviceListWithScrollIndicators lookbackInDays={lookbackInDays} />
```

## ✅ **Result**

### **Before Fix:**
- **PageContent**: `useDeviceState(7)` → Query Key: `["device-state", 7]`
- **DeviceList**: `useDeviceState(30)` → Query Key: `["device-state", 30]`
- **Result**: 2 separate API calls

### **After Fix:**
- **PageContent**: `useDeviceState(7)` → Query Key: `["device-state", 7]`
- **DeviceList**: `useDeviceState(7)` → Query Key: `["device-state", 7]`
- **Result**: 1 shared API call (React Query cache hit)

## 🚀 **Performance Impact**

### **Network Requests:**
- **Before**: 4 API calls (2 components × 2 endpoints each)
- **After**: 2 API calls (shared cache between components)
- **Improvement**: 50% reduction in API calls

### **User Experience:**
- **Faster loading**: Second component loads instantly from cache
- **Consistent data**: Both components show same time range data
- **Real-time sync**: Time range changes update both components simultaneously

## 🔧 **How React Query Caching Works**

### **Query Key Matching:**
```typescript
// These are DIFFERENT queries (separate cache entries):
useQuery({ queryKey: ["device-state", 7] })   // Component A
useQuery({ queryKey: ["device-state", 30] })  // Component B

// These are SAME query (shared cache entry):
useQuery({ queryKey: ["device-state", 7] })   // Component A
useQuery({ queryKey: ["device-state", 7] })   // Component B (cache hit!)
```

### **Cache Behavior:**
1. **First component** calls `useDeviceState(7)` → API call + cache store
2. **Second component** calls `useDeviceState(7)` → Cache hit (no API call)
3. **Time range change** → Both components get fresh data from single API call

## 🧪 **How to Verify the Fix**

### **1. Open Browser DevTools**
- Navigate to Network tab
- Clear network log

### **2. Load Dashboard**
- Go to `http://localhost:3001/`
- Check network requests

### **3. Expected Behavior:**
- **Before Fix**: 4 requests (2 × `/things`, 2 × `/latestEventTime`)
- **After Fix**: 2 requests (1 × `/things`, 1 × `/latestEventTime`)

### **4. Test Time Range Changes:**
- Change time range selector
- Should see only 2 new requests (not 4)
- Both components update simultaneously

## 📋 **Key Learnings**

### **React Query Best Practices:**
1. **Consistent Query Keys**: Ensure components using same data use same query keys
2. **Parameter Coordination**: When multiple components need same data, coordinate parameters
3. **Cache Optimization**: Leverage React Query's caching by avoiding unnecessary key variations

### **Component Communication:**
1. **Prop Drilling**: Sometimes necessary to ensure cache efficiency
2. **Shared State**: Time range should be shared between related components
3. **Performance Monitoring**: Always check network tab for duplicate requests

## 🎯 **Future Considerations**

### **Alternative Solutions:**
1. **Context Provider**: Could create a shared device state context
2. **Higher-Order Component**: Wrap both components with shared data provider
3. **Custom Hook**: Create a dashboard-specific hook that manages shared state

### **Monitoring:**
- Add React Query DevTools to visualize cache behavior
- Monitor network requests in production
- Consider adding query key logging for debugging

This fix demonstrates the importance of understanding React Query's caching mechanism and ensuring components that need the same data use consistent query keys.
