{"name": "aerovy", "version": "0.1.0", "private": true, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@headlessui/react": "^2.1.10", "@mui/material": "^5.14.14", "@mui/x-date-pickers": "^6.16.3", "@mui/x-date-pickers-pro": "^6.16.2", "@syncfusion/ej2-calendars": "^23.1.41", "@tanstack/react-query": "^5.85.3", "@tanstack/react-table": "^8.21.3", "@testing-library/user-event": "^13.5.0", "@types/node": "^20.12.2", "@types/react": "^18.2.73", "@types/react-dom": "^18.2.23", "@wojtekmaj/react-timerange-picker": "^5.4.3", "amazon-cognito-identity-js": "^6.3.2", "aws-sdk": "^2.1382.0", "dayjs": "^1.11.10", "deck.gl": "^8.9.7", "dotenv": "^16.0.3", "fuse.js": "^7.1.0", "http-proxy-middleware": "^2.0.6", "mapbox-gl": "^3.13.0", "prismjs": "^1.30.0", "react": "^18.2.0", "react-calendar": "^4.2.1", "react-datetime": "^3.2.0", "react-dom": "^18.2.0", "react-dropdown": "^1.11.0", "react-icons": "^4.8.0", "react-map-gl": "^7.1.3", "react-percent-bar": "^0.0.2", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "react-select": "^5.7.3", "react-simple-maps": "^3.0.0", "react-tooltip": "^5.21.5", "react-vertical-timeline-component": "^3.6.0", "recharts": "^2.15.0", "rsuite": "^5.40.0", "tailwind-scrollbar": "^3.0.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zustand": "^5.0.3"}, "scripts": {"start": "REACT_APP_ENV=local react-scripts start", "build": "REACT_APP_ENV=production react-scripts build", "build:production": "REACT_APP_ENV=production react-scripts build", "build:staging": "REACT_APP_ENV=staging react-scripts build", "build:development": "REACT_APP_ENV=development react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "check": "biome check --write src", "format": "biome format --write src", "lint": "biome lint --write src", "check:ci": "biome check --changed --no-errors-on-unmatched src", "test:ci": "jest", "test:watch": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "not safari < 10", "not chrome < 51", "not android < 5", "not ie < 12"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@biomejs/biome": "1.9.4", "@storybook/addon-a11y": "^8.6.12", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/addon-styling": "^1.3.7", "@storybook/addon-themes": "^8.6.12", "@storybook/addon-viewport": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/preset-create-react-app": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-webpack5": "^8.6.12", "@storybook/test": "^8.6.12", "@tailwindcss/container-queries": "^0.1.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "babel-loader": "^10.0.0", "jest": "^27.5.1", "jest-environment-jsdom": "^29.7.0", "prop-types": "^15.8.1", "react-docgen-typescript": "^2.2.2", "storybook": "^8.6.12", "tailwindcss": "^3.2.7", "webpack": "^5.99.8"}}