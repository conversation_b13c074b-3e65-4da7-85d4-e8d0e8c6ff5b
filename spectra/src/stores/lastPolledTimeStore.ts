import type { Dayjs } from "utils/dayjs";
import { create } from "zustand";

interface DevicePollingState {
  currentPolledDeviceStartTime: Dayjs | null;

  // Data indexed by thingId
  lastEventTime: Record<string, Dayjs | null>;

  // Actions
  setCurrentPolledDeviceStartTime: (startTime: Dayjs | null) => void;
  setLastEventTime: (thingId: string, lastEventTime: Dayjs | null) => void;
}

export const useLastPolledTimeStore = create<DevicePollingState>((set) => ({
  // Initial state
  currentPolledDeviceStartTime: null,
  lastEventTime: {},

  // Actions
  setCurrentPolledDeviceStartTime: (startTime) =>
    set({ currentPolledDeviceStartTime: startTime }),
  setLastEventTime: (thingId, lastEventTime) =>
    set((state) => ({
      lastEventTime: {
        ...state.lastEventTime,
        [thingId]: lastEventTime,
      },
    })),
}));
