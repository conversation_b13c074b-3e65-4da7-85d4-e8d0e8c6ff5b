import { type DatapointMap, type MetricType, datapointsToMap } from "api/data";
import type { Thing } from "api/ingestion/things";
import { useAuth } from "context/AuthContext";
import { useSelectedSimulation } from "context/SelectedSimulationContext";
import { useSelectedTimeRange } from "context/SelectedTimeRangeContext";
import { useEffect, useMemo, useState } from "react";
import { useLastPolledTimeStore } from "stores/lastPolledTimeStore";
import { type Dayjs, dayjs } from "utils/dayjs";
import { useDataApi } from "../api/data";
import { useBulkDataApi } from "../api/data/bulk";
import { ErrorPatterns } from "../errorManagement";

// Hook for fetching stats for a single device (legacy, kept for backward compatibility)
export const useDeviceStats = (device: Thing) => {
  // state
  const [stats, setStats] = useState<DatapointMap>({});
  const [localLastEventTime, setLocalLastEventTime] = useState<string | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);
  const lastEventTime: Dayjs | null = useLastPolledTimeStore(
    (state) => state.lastEventTime[device.thingId],
  );

  // contexts
  const { start, end } = useSelectedTimeRange();
  const { simulationId } = useSelectedSimulation();
  const { user } = useAuth();

  // api
  const { getSummaryForThing, getThingLastEventTime } = useDataApi();

  // derived
  const reportingInterval = Number(device.properties?.reportingInterval) ?? 60;

  const isDisconnected = useMemo(() => {
    const threshold = dayjs().subtract(reportingInterval, "minutes"); // now - reportingInterval
    const isConnected =
      lastEventTime?.isAfter(threshold) ||
      (localLastEventTime && dayjs(localLastEventTime).isAfter(threshold));

    return !isConnected;
  }, [lastEventTime, localLastEventTime, reportingInterval]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to fetch stats on mount; getSummaryForThing and user are not a dependency
  useEffect(() => {
    if (!user) return;

    const fetchStats = async () => {
      setIsLoading(true);

      const result = await ErrorPatterns.withErrorHandling(async () => {
        const response = await getSummaryForThing(
          device.placeType,
          device.placeId,
          device.thingId,
          start,
          end,
          simulationId,
        );
        return datapointsToMap(response);
      }, `Failed to load device statistics for ${device.thingName} (${device.thingId}). Device may be offline.`);

      if (result) {
        // filter out stats that have unit string
        const filteredStats = Object.fromEntries(
          Object.entries(result).filter(
            ([_, value]) => value.unit !== "string",
          ),
        );
        setStats(filteredStats);
      } else {
        // Error was already handled by the centralized system
        // Set empty stats to show "No data" state
        setStats({});
      }

      setIsLoading(false);
    };

    fetchStats();
  }, [device, start, end, user?.partnerId, simulationId]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to refetch stats when last event time changes
  useEffect(() => {
    if (!lastEventTime || isLoading || !user) return;

    const fetchStats = async () => {
      await getSummaryForThing(
        device.placeType,
        device.placeId,
        device.thingId,
        lastEventTime, // note: the last event time is the start time so we can ensure we get the latest stats
        lastEventTime.add(1, "minutes"),
        simulationId,
      )
        .then(datapointsToMap)
        .then((response) => {
          // filter out stats that have unit string
          return Object.fromEntries(
            Object.entries(response).filter(
              ([_, value]) => value.unit !== "string",
            ),
          );
        })
        .then(setStats)
        .catch((e) => console.log("refetching failed", e));
    };

    fetchStats();
  }, [lastEventTime?.toString()]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to fetch last event time on mount; getThingLastEventTime is not a dependency
  useEffect(() => {
    if (!user) return;

    const fetchLastEventTime = async () => {
      const result = await ErrorPatterns.withErrorHandling(async () => {
        return await getThingLastEventTime(
          device.placeType,
          device.placeId,
          device.thingId,
        );
      }, `Failed to check connection status for ${device.thingName} (${device.thingId}). Connection indicator may not be accurate.`);

      // Handle the response - result will be null if there was an error
      setLocalLastEventTime(result || null);
    };

    fetchLastEventTime();
  }, [device, user]);

  return {
    stats,
    isLoading,
    lastEventTime: localLastEventTime,
    isDisconnected,
    reportingInterval,
  };
};

// New hook for fetching stats for all devices at once using bulk API
export const useAllDeviceStats = (devices: Thing[]) => {
  const [allStats, setAllStats] = useState<Record<string, DatapointMap>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [metricKeys, setMetricKeys] = useState<Record<string, MetricType[]>>(
    {},
  );
  const { start, end } = useSelectedTimeRange();
  const { user } = useAuth();
  const { getAllThingSummaries } = useBulkDataApi();
  const { getThingMetricKeys } = useDataApi();

  // Fetch metric keys to get proper unit information
  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to fetch metric keys on mount; getThingMetricKeys is not a dependency
  useEffect(() => {
    if (!user) return;

    const fetchMetricKeys = async () => {
      try {
        const keys = await getThingMetricKeys();
        setMetricKeys(keys);
      } catch (error) {
        console.error("Failed to fetch metric keys:", error);
        // Continue without metric keys, will use default units
      }
    };

    fetchMetricKeys();
  }, [user]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to fetch stats on mount; getAllThingSummaries is not a dependency
  useEffect(() => {
    if (!user || devices.length === 0 || Object.keys(metricKeys).length === 0) {
      setIsLoading(false);
      return;
    }

    const fetchAllStats = async () => {
      setIsLoading(true);

      const result = await ErrorPatterns.withErrorHandling(async () => {
        return await getAllThingSummaries(start, end);
      }, "Failed to load device statistics. Some devices may be offline.");

      if (result) {
        // Process the bulk response - bulk API doesn't include unit property
        // so we need to convert the bulk datapoints to the main API format
        const processedStats: Record<string, DatapointMap> = {};

        for (const [thingId, datapoints] of Object.entries(result)) {
          // Convert bulk datapoints to main API datapoints with proper unit property
          const convertedDatapoints = datapoints.map((dp) => {
            // Find the device type to get the correct metric keys
            const device = devices.find((d) => d.thingId === thingId);
            const deviceType = device?.thingType || "unknown"; // This should probably be a failure case
            const deviceMetrics = metricKeys[deviceType] || [];

            // Find the metric definition to get the proper unit
            const metricDef = deviceMetrics.find((m) => m.name === dp.type);
            const unit = metricDef?.unit || ""; // Fallback to "" if not found

            return {
              ...dp,
              unit,
            };
          });

          const datapointMap = datapointsToMap(convertedDatapoints);
          // Filter out stats that have unit string
          const filteredStats = Object.fromEntries(
            Object.entries(datapointMap).filter(
              ([_, value]) => value.unit !== "string",
            ),
          );
          processedStats[thingId] = filteredStats;
        }

        setAllStats(processedStats);
      } else {
        // Error was already handled by the centralized system
        setAllStats({});
      }

      setIsLoading(false);
    };

    fetchAllStats();
  }, [devices, start, end, user, metricKeys]);

  return {
    allStats,
    isLoading,
  };
};
