/**
 * Hook that enhances device data with device states, lastEventTime, and reportingInterval
 *
 * @example
 * ```typescript
 * import { useDeviceState, DeviceStates } from "hooks/useDeviceState";
 *
 * const MyComponent = () => {
 *   const { devices, isLoading, error } = useDeviceState();
 *
 *   const onlineDevices = devices.filter(device => device.deviceState === DeviceStates.ONLINE);
 *   const offlineDevices = devices.filter(device => device.deviceState === DeviceStates.OFFLINE);
 *
 *   if (isLoading) return <div>Loading...</div>;
 *   if (error) return <div>Error: {error.message}</div>;
 *
 *   return (
 *     <div>
 *       <h2>Online Devices: {onlineDevices.length}</h2>
 *       <h2>Offline Devices: {offlineDevices.length}</h2>
 *       {devices.map(device => (
 *         <div key={device.thingId}>
 *           <h3>{device.thingName}</h3>
 *           <p>State: {device.deviceState}</p>
 *           <p>Last Event: {device.lastEventTime}</p>
 *           <p>Reporting Interval: {device.reportingInterval}s</p>
 *         </div>
 *       ))}
 *     </div>
 *   );
 * };
 * ```
 */

import { useQuery } from "@tanstack/react-query";
import { useBulkDataApi } from "api/data/bulk";
import { useThingsApi } from "api/ingestion/things";
import type { Thing } from "api/ingestion/things";

export enum DeviceStates {
  ONLINE = "online",
  OFFLINE = "offline",
  UNKNOWN = "unknown",
  UNINITIALIZED = "uninitialized",
}

export interface EnhancedDevice extends Thing {
  deviceState: DeviceStates;
  lastEventTime: string | null;
  reportingInterval: number | null;
}

/**
 * Helper function to enhance devices with state information
 */
const enhanceDevicesWithState = (
  devices: Thing[],
  lastEventTimesMap: Record<string, string>,
): EnhancedDevice[] => {
  return devices.map((device) => {
    // Get reporting interval from device properties
    const reportingInterval = device.properties?.reportingInterval
      ? Number(device.properties.reportingInterval)
      : null;

    // Get lastEventTime from the bulk response map
    const lastEventTime = lastEventTimesMap[device.thingId] || "";

    // If lastEventTime is null, empty return UNINITIALIZED
    if (!lastEventTime || lastEventTime === "") {
      return {
        ...device,
        deviceState: DeviceStates.UNINITIALIZED,
        lastEventTime: lastEventTime,
        reportingInterval: reportingInterval,
      } as EnhancedDevice;
    }

    // If lastEventTime is not a valid date, return UNINITIALIZED
    const parseTime = (time: string): number | null => {
      try {
        const date = new Date(time).getTime();
        if (!Number.isNaN(date)) {
          return date;
        }
      } catch (e) {
        console.log("Error parsing date:", time, e);
      }

      return null;
    };

    const lastEvent = parseTime(lastEventTime);
    if (lastEvent === null) {
      return {
        ...device,
        deviceState: DeviceStates.UNINITIALIZED,
        lastEventTime: lastEventTime,
        reportingInterval: reportingInterval,
      } as EnhancedDevice;
    }

    // If reportingInterval is null, return UNKNOWN
    if (reportingInterval === null) {
      return {
        ...device,
        deviceState: DeviceStates.UNKNOWN,
        lastEventTime: lastEventTime,
        reportingInterval: null,
      } as EnhancedDevice;
    }

    // If lastEventTime is a valid date, return ONLINE or OFFLINE
    const now = new Date().getTime();
    const timeElapsed = now - lastEvent;
    const isOnline = timeElapsed < reportingInterval * 60 * 1000;

    return {
      ...device,
      deviceState: isOnline ? DeviceStates.ONLINE : DeviceStates.OFFLINE,
      lastEventTime,
      reportingInterval,
    } as EnhancedDevice;
  });
};

export const useDeviceState = (lookbackInDays = 30) => {
  const { getThings } = useThingsApi();
  const { getAllThingLastEventTimes } = useBulkDataApi();

  const {
    data: enhancedDevices = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["device-state", lookbackInDays],
    queryFn: async (): Promise<EnhancedDevice[]> => {
      try {
        // Fetch both devices and event times in parallel
        const [devices, lastEventTimesMap] = await Promise.all([
          getThings(),
          getAllThingLastEventTimes(lookbackInDays),
        ]);

        return enhanceDevicesWithState(devices, lastEventTimesMap);
      } catch (error) {
        console.error("Error fetching enhanced devices:", error);
        throw error;
      }
    },
    staleTime: 30000, // Consider data fresh for 30 seconds
    refetchInterval: 60000, // Auto-refresh every minute for real-time status
    retry: 2, // Retry failed requests 2 times
  });

  return {
    devices: enhancedDevices,
    isLoading,
    error,
    refetch, // Expose refetch for manual refresh
  };
};
