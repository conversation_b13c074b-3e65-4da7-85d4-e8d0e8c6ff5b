/**
 * Hook that enhances device data with device states, lastEventTime, and reportingInterval
 *
 * @example
 * ```typescript
 * import { useDeviceState, DeviceStates } from "hooks/useDeviceState";
 *
 * const MyComponent = () => {
 *   const { devices, isLoading } = useDeviceState();
 *
 *   const onlineDevices = devices.filter(device => device.deviceState === DeviceStates.ONLINE);
 *   const offlineDevices = devices.filter(device => device.deviceState === DeviceStates.OFFLINE);
 *
 *   if (isLoading) return <div>Loading...</div>;
 *
 *   return (
 *     <div>
 *       <h2>Online Devices: {onlineDevices.length}</h2>
 *       <h2>Offline Devices: {offlineDevices.length}</h2>
 *       {devices.map(device => (
 *         <div key={device.thingId}>
 *           <h3>{device.thingName}</h3>
 *           <p>State: {device.deviceState}</p>
 *           <p>Last Event: {device.lastEventTime}</p>
 *           <p>Reporting Interval: {device.reportingInterval}s</p>
 *         </div>
 *       ))}
 *     </div>
 *   );
 * };
 * ```
 */

import { useBulkDataApi } from "api/data/bulk";
import type { Thing } from "api/ingestion/things";
import { useEffect, useState } from "react";
import { useAppData } from "./useAppData";

export enum DeviceStates {
  ONLINE = "online",
  OFFLINE = "offline",
  UNKNOWN = "unknown",
  UNINITIALIZED = "uninitialized",
}

export interface EnhancedDevice extends Thing {
  deviceState: DeviceStates;
  lastEventTime: string | null;
  reportingInterval: number | null;
}

export const useDeviceState = (lookbackInDays = 30) => {
  const { devices, fetchDevicesIfNeeded, isLoading } = useAppData();
  const [enhancedDevices, setEnhancedDevices] = useState<EnhancedDevice[]>([]);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const { getAllThingLastEventTimes } = useBulkDataApi();

  // biome-ignore lint/correctness/useExhaustiveDependencies: fetchDevicesIfNeeded is not a dependency of the useEffect hook
  useEffect(() => {
    fetchDevicesIfNeeded();
  }, []);

  // TODO: this is not great... we should make this more robust if we intend on using it
  // biome-ignore lint/correctness/useExhaustiveDependencies: isLoading and getAllThingLastEventTimes are not dependencies of the useEffect hook
  useEffect(() => {
    if (isLoading.devices || devices.length === 0) {
      setEnhancedDevices([]);
      return;
    }

    const enhanceDevices = async () => {
      setIsEnhancing(true);

      try {
        // Fetch all last event times in a single bulk call
        const lastEventTimesMap =
          await getAllThingLastEventTimes(lookbackInDays);

        const enhancedDevicesResult = devices.map((device) => {
          // Get reporting interval from device properties
          const reportingInterval = device.properties?.reportingInterval
            ? Number(device.properties.reportingInterval)
            : null;

          // Get lastEventTime from the bulk response map
          const lastEventTime = lastEventTimesMap[device.thingId] || "";

          // If lastEventTime is null, empty return UNINITIALIZED
          if (!lastEventTime || lastEventTime === "") {
            return {
              ...device,
              deviceState: DeviceStates.UNINITIALIZED,
              lastEventTime: lastEventTime,
              reportingInterval: reportingInterval,
            } as EnhancedDevice;
          }

          // If lastEventTime is not a valid date, return UNINITIALIZED
          const parseTime = (time: string): number | null => {
            try {
              const date = new Date(time).getTime();
              if (!Number.isNaN(date)) {
                return date;
              }
            } catch (e) {
              console.log("Error parsing date:", time, e);
            }

            return null;
          };

          const lastEvent = parseTime(lastEventTime);
          if (lastEvent === null) {
            return {
              ...device,
              deviceState: DeviceStates.UNINITIALIZED,
              lastEventTime: lastEventTime,
              reportingInterval: reportingInterval,
            } as EnhancedDevice;
          }

          // If reportingInterval is null, return UNKNOWN
          if (reportingInterval === null) {
            return {
              ...device,
              deviceState: DeviceStates.UNKNOWN,
              lastEventTime: lastEventTime,
              reportingInterval: null,
            } as EnhancedDevice;
          }

          // If lastEventTime is a valid date, return ONLINE or OFFLINE
          const now = new Date().getTime();
          const timeElapsed = now - lastEvent;
          const isOnline = timeElapsed < reportingInterval * 60 * 1000;

          return {
            ...device,
            deviceState: isOnline ? DeviceStates.ONLINE : DeviceStates.OFFLINE,
            lastEventTime,
            reportingInterval,
          } as EnhancedDevice;
        });

        setEnhancedDevices(enhancedDevicesResult);
      } catch (error) {
        console.error("Error enhancing devices:", error);
        // Fallback to basic device data with UNKNOWN state
        const fallbackDevices = devices.map((device) => ({
          ...device,
          deviceState: DeviceStates.UNKNOWN,
          lastEventTime: null,
          reportingInterval: null,
        })) as EnhancedDevice[];
        setEnhancedDevices(fallbackDevices);
      } finally {
        setIsEnhancing(false);
      }
    };

    enhanceDevices();
  }, [devices, lookbackInDays]);

  return {
    devices: enhancedDevices,
    isLoading: isLoading.devices || isEnhancing,
    fetchDevicesIfNeeded,
  };
};
