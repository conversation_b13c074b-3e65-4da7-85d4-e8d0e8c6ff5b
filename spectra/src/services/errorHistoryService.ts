// IndexedDB service for error history CRUD operations

export interface ErrorHistoryEntry {
  id: string;
  timestamp: Date;
  message: string;
  type: "network" | "validation" | "auth" | "server" | "unknown";
  severity: "low" | "medium" | "high" | "critical";
  source?: string; // Component or page where error occurred
  stack?: string; // Error stack trace if available
  userAgent?: string;
  url?: string; // Current page URL when error occurred
  endpoint?: string; // API endpoint that caused the error
  httpMethod?: string; // HTTP method used (GET, POST, etc.)
  httpStatus?: number; // HTTP status code (400, 500, etc.)
  userId?: string; // User ID if available
  metadata?: Record<string, unknown>; // Additional error context
}

class ErrorHistoryService {
  private dbName = "ErrorHistoryDB";
  private dbVersion = 1;
  private storeName = "errorHistory";
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        reject(new Error("Failed to open IndexedDB"));
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: "id" });

          // Create indexes for efficient querying
          store.createIndex("timestamp", "timestamp", { unique: false });
          store.createIndex("type", "type", { unique: false });
          store.createIndex("severity", "severity", { unique: false });
          store.createIndex("source", "source", { unique: false });
        }
      };
    });
  }

  private ensureDB(): IDBDatabase {
    if (!this.db) {
      throw new Error("Database not initialized. Call init() first.");
    }
    return this.db;
  }

  async addError(error: Omit<ErrorHistoryEntry, "id">): Promise<string> {
    const db = this.ensureDB();
    const id = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const errorEntry: ErrorHistoryEntry = {
      ...error,
      id,
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);
      const request = store.add(errorEntry);

      request.onsuccess = () => {
        resolve(id);
      };

      request.onerror = () => {
        reject(new Error("Failed to add error to database"));
      };
    });
  }

  async getErrors(options?: {
    limit?: number;
    offset?: number;
    type?: string;
    severity?: string;
    startDate?: Date;
    endDate?: Date;
  }): Promise<ErrorHistoryEntry[]> {
    const db = this.ensureDB();
    const {
      limit = 50,
      offset = 0,
      type,
      severity,
      startDate,
      endDate,
    } = options || {};

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], "readonly");
      const store = transaction.objectStore(this.storeName);
      const index = store.index("timestamp");

      // Get all records in reverse chronological order
      const request = index.openCursor(null, "prev");
      const results: ErrorHistoryEntry[] = [];

      let skipped = 0;

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;

        if (!cursor || results.length >= limit) {
          resolve(results);
          return;
        }

        const entry: ErrorHistoryEntry = cursor.value;

        // Apply filters
        let shouldInclude = true;

        if (type && entry.type !== type) shouldInclude = false;
        if (severity && entry.severity !== severity) shouldInclude = false;
        if (startDate && entry.timestamp < startDate) shouldInclude = false;
        if (endDate && entry.timestamp > endDate) shouldInclude = false;

        if (shouldInclude) {
          if (skipped < offset) {
            skipped++;
          } else {
            results.push(entry);
          }
        }

        cursor.continue();
      };

      request.onerror = () => {
        reject(new Error("Failed to retrieve errors from database"));
      };
    });
  }

  async getErrorById(id: string): Promise<ErrorHistoryEntry | null> {
    const db = this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], "readonly");
      const store = transaction.objectStore(this.storeName);
      const request = store.get(id);

      request.onsuccess = () => {
        resolve(request.result || null);
      };

      request.onerror = () => {
        reject(new Error("Failed to retrieve error from database"));
      };
    });
  }

  async deleteError(id: string): Promise<void> {
    const db = this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);
      const request = store.delete(id);

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        reject(new Error("Failed to delete error from database"));
      };
    });
  }

  async clearAllErrors(): Promise<void> {
    const db = this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);
      const request = store.clear();

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        reject(new Error("Failed to clear errors from database"));
      };
    });
  }

  async getErrorStats(): Promise<{
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    last24Hours: number;
  }> {
    const db = this.ensureDB();
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], "readonly");
      const store = transaction.objectStore(this.storeName);
      const request = store.getAll();

      request.onsuccess = () => {
        const errors: ErrorHistoryEntry[] = request.result;

        const stats = {
          total: errors.length,
          byType: {} as Record<string, number>,
          bySeverity: {} as Record<string, number>,
          last24Hours: 0,
        };

        for (const error of errors) {
          // Count by type
          stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;

          // Count by severity
          stats.bySeverity[error.severity] =
            (stats.bySeverity[error.severity] || 0) + 1;

          // Count last 24 hours
          if (error.timestamp >= last24Hours) {
            stats.last24Hours++;
          }
        }

        resolve(stats);
      };

      request.onerror = () => {
        reject(new Error("Failed to get error statistics"));
      };
    });
  }
}

// Export singleton instance
export const errorHistoryService = new ErrorHistoryService();

// Initialize the service when the module is loaded
errorHistoryService.init().catch(console.error);
