/**
 * Utility functions for error handling and classification
 */

import {
  type AppError,
  type AuthenticationError,
  ERROR_CONFIGS,
  ERROR_MESSAGES,
  type ErrorCategory,
  ErrorSeverity,
  HTTP_STATUS_TO_CATEGORY,
  type NetworkError,
  type RuntimeError,
  type ServerError,
  type ValidationError,
} from "../types/errors";

/**
 * Generates a unique ID for errors
 * This replaces UUID to avoid Storybook compatibility issues
 */
export function generateErrorId(): string {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 8);
  return `error_${timestamp}_${randomPart}`;
}

/**
 * Creates a standardized error object
 */
export function createError<T extends AppError = AppError>(
  title: string,
  message: string,
  category: ErrorCategory,
  options: Partial<T> = {} as Partial<T>,
): T {
  const config = ERROR_CONFIGS[category];

  return {
    id: generateErrorId(),
    title,
    message,
    category,
    severity: options.severity || config.defaultSeverity,
    timestamp: new Date(),
    dismissible: options.dismissible ?? config.defaultDismissible,
    autoHide: options.autoHide ?? config.defaultAutoHide,
    autoHideDelay: options.autoHideDelay ?? config.defaultAutoHideDelay,
    ...options,
  } as T;
}

/**
 * Creates a network error from HTTP response
 */
export function createNetworkError(
  response: Response,
  url?: string,
  method?: string,
): NetworkError {
  const category = HTTP_STATUS_TO_CATEGORY[response.status] || "network";
  let title = "Network Error";
  let message: string = ERROR_MESSAGES.UNKNOWN_ERROR;

  switch (response.status) {
    case 401:
      title = "Authentication Required";
      message = ERROR_MESSAGES.AUTHENTICATION_REQUIRED;
      break;
    case 403:
      title = "Access Denied";
      message = ERROR_MESSAGES.AUTHORIZATION_DENIED;
      break;
    case 404:
      title = "Not Found";
      message = "The requested resource was not found.";
      break;
    case 408:
    case 504:
      title = "Request Timeout";
      message = ERROR_MESSAGES.NETWORK_TIMEOUT;
      break;
    case 429:
      title = "Too Many Requests";
      message = "Please wait a moment before trying again.";
      break;
    case 500:
    case 502:
    case 503:
      title = "Server Error";
      message = ERROR_MESSAGES.SERVER_ERROR;
      break;
    default:
      if (response.status >= 400 && response.status < 500) {
        title = "Client Error";
        message = "There was a problem with your request.";
      } else if (response.status >= 500) {
        title = "Server Error";
        message = ERROR_MESSAGES.SERVER_ERROR;
      }
  }

  return createError<NetworkError>(title, message, category, {
    statusCode: response.status,
    url,
    method,
    retryable:
      response.status >= 500 ||
      response.status === 408 ||
      response.status === 504,
  });
}

/**
 * Creates an authentication error
 */
export function createAuthenticationError(
  message = ERROR_MESSAGES.AUTHENTICATION_REQUIRED,
  redirectToLogin = true,
): AuthenticationError {
  return createError<AuthenticationError>(
    "Authentication Required",
    message,
    "authentication",
    {
      redirectToLogin,
      severity: "high",
      dismissible: false,
    },
  );
}

/**
 * Creates a validation error
 */
export function createValidationError(
  message = ERROR_MESSAGES.VALIDATION_FAILED,
  field?: string,
  fieldErrors?: Record<string, string[]>,
): ValidationError {
  return createError<ValidationError>(
    "Validation Error",
    message,
    "validation",
    {
      field,
      fieldErrors,
    },
  );
}

/**
 * Creates a server error
 */
export function createServerError(
  statusCode: number,
  message = ERROR_MESSAGES.SERVER_ERROR,
  errorCode?: string,
): ServerError {
  return createError<ServerError>("Server Error", message, "server", {
    statusCode,
    errorCode,
    retryable: true,
  });
}

/**
 * Creates a runtime error from JavaScript Error
 */
export function createRuntimeError(
  error: Error,
  componentStack?: string,
): RuntimeError {
  return createError<RuntimeError>("Runtime Error", error.message, "runtime", {
    stack: error.stack,
    componentStack,
    severity: "critical",
  });
}

/**
 * Determines if an error is retryable
 */
export function isRetryableError(error: AppError): boolean {
  return error.retryable === true;
}

/**
 * Determines if an error should auto-hide
 */
export function shouldAutoHide(error: AppError): boolean {
  return error.autoHide === true && error.autoHideDelay > 0;
}

/**
 * Gets user-friendly error message based on error type
 */
export function getUserFriendlyMessage(error: unknown): string {
  if (typeof error === "string") {
    return error;
  }

  if (typeof error === "object" && error !== null) {
    if (
      "message" in error &&
      typeof (error as { message?: unknown }).message === "string"
    ) {
      return (error as { message: string }).message;
    }

    if (
      "response" in error &&
      typeof (error as { response?: unknown }).response === "object" &&
      (error as { response?: unknown }).response !== null &&
      "status" in (error as { response: { status?: unknown } }).response
    ) {
      const status = (error as { response: { status: number } }).response
        .status;
      switch (status) {
        case 401:
          return ERROR_MESSAGES.AUTHENTICATION_REQUIRED;
        case 403:
          return ERROR_MESSAGES.AUTHORIZATION_DENIED;
        case 404:
          return "The requested resource was not found.";
        case 408:
        case 504:
          return ERROR_MESSAGES.NETWORK_TIMEOUT;
        case 500:
        case 502:
        case 503:
          return ERROR_MESSAGES.SERVER_ERROR;
        default:
          return ERROR_MESSAGES.UNKNOWN_ERROR;
      }
    }
  }

  return ERROR_MESSAGES.UNKNOWN_ERROR;
}

/**
 * Checks if the user is offline
 */
export function isOffline(): boolean {
  return !navigator.onLine;
}

/**
 * Creates an offline error
 */
export function createOfflineError(): NetworkError {
  return createError<NetworkError>(
    "Connection Lost",
    ERROR_MESSAGES.NETWORK_OFFLINE,
    "network",
    {
      severity: "high",
      autoHide: false,
    },
  );
}
