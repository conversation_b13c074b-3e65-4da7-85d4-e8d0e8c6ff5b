/**
 * Test utility to demonstrate enhanced error tracking with endpoint information
 */

import { useAuthFetch } from "../context/AuthContext";

/**
 * Function to test API error tracking with endpoint information
 * This will trigger an API error and demonstrate that endpoint info is captured
 */
export const testApiErrorTracking = async () => {
  try {
    const { authFetch } = useAuthFetch();

    // This will likely result in a 404 or other error, demonstrating endpoint tracking
    const response = await authFetch("/api/test-endpoint-that-does-not-exist", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ test: "data" }),
    });

    if (!response.ok) {
      console.log(
        "API error occurred - check Error History to see endpoint information",
      );
    }

    return response;
  } catch (error) {
    console.log(
      "Network error occurred - check Error History to see endpoint information",
    );
    throw error;
  }
};

/**
 * Function to test different types of API errors
 */
export const testDifferentApiErrors = async () => {
  const { authFetch } = useAuthFetch();

  const testEndpoints = [
    { url: "/api/test-404", method: "GET" },
    { url: "/api/test-500", method: "POST" },
    { url: "/api/test-401", method: "PUT" },
    { url: "/api/test-validation", method: "PATCH" },
  ];

  for (const endpoint of testEndpoints) {
    try {
      await authFetch(endpoint.url, {
        method: endpoint.method,
        headers: { "Content-Type": "application/json" },
        body:
          endpoint.method !== "GET"
            ? JSON.stringify({ test: "data" })
            : undefined,
      });
    } catch (error) {
      console.log(`Error testing ${endpoint.method} ${endpoint.url}:`, error);
    }
  }
};
