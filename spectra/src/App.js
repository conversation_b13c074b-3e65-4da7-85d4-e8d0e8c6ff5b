import { Sidebar } from "components";
import { useAuth } from "context/AuthContext";
import "mapbox-gl/dist/mapbox-gl.css";
import {
  ApiTokensPage,
  Dashboard,
  DebugPage,
  DeviceDetail,
  DevicesPage,
  ErrorHistoryPage,
  FleetDetail,
  IntegrationDetailPage,
  IntegrationsPage,
  LocationSelection,
  Login,
  MonitorsPage,
  MyOrganizationPage,
  NoAccessPage,
  OrganizationPage,
  ResponsePage,
  ScopeSelection,
  Settings,
  SiteDetail,
  ThingMonitorDetailPage,
  ThirdParty,
  Transaction,
} from "pages";

import { JumpBar } from "components/JumpBar";
import { ErrorProvider } from "components/uikit/ErrorProvider/ErrorProvider";
import { SearchHintOverlay } from "components/uikit/SearchHintOverlay/SearchHintOverlay";
import { JumpBarProvider } from "context/JumpBarContext";
import { setupErrorManagement } from "errorManagement/index";

import { SelectedDeviceProvider } from "context/SelectedDeviceContext";
import { SelectedTimeRangeProvider } from "context/SelectedTimeRangeContext";
import { Navigate, Route, Routes } from "react-router-dom";
import { useErrorStore } from "stores/errorStore";

const App = () => {
  const { isLoading, token, user } = useAuth();
  const hasNoAccessError = useErrorStore((state) => state.hasNoAccessError);
  setupErrorManagement();

  if (isLoading) {
    return <div>Loading ...</div>;
  }

  if (!token) {
    return (
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/" element={<Login />} />
        <Route path="*" element={!user && <Navigate to="/login" replace />} />
      </Routes>
    );
  }

  return (
    <ErrorProvider toastPosition="top-right" maxToasts={5}>
      <SelectedTimeRangeProvider>
        <SelectedDeviceProvider>
          <JumpBarProvider>
            <div className="flex flex-row w-screen bg-white">
              <Sidebar />
              <div className="min-h-screen grow min-w-0">
                {hasNoAccessError ? (
                  <NoAccessPage />
                ) : (
                  <Routes>
                    <Route path="/" element={<Dashboard />} />
                    <Route
                      path="/sites"
                      element={<LocationSelection locationType="site" />}
                    />
                    <Route path="/sites/:siteId" element={<SiteDetail />} />
                    <Route
                      path="/fleets"
                      element={<LocationSelection locationType="fleet" />}
                    />
                    <Route path="/fleets/:fleetId" element={<FleetDetail />} />
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/transaction" element={<Transaction />} />
                    <Route path="/settings" element={<Settings />} />
                    <Route path="/alerts" element={<NoAccessPage />} />
                    <Route path="/devices" element={<DevicesPage />} />
                    <Route
                      path="/devices/:deviceId"
                      element={<DeviceDetail />}
                    />
                    <Route path="/monitors" element={<MonitorsPage />} />
                    <Route
                      path="/monitors/:thingId/:monitorId"
                      element={<ThingMonitorDetailPage />}
                    />
                    <Route
                      path="/integrations"
                      element={<IntegrationsPage />}
                    />
                    <Route
                      path="/integrations/:integrationId"
                      element={<IntegrationDetailPage />}
                    />
                    <Route path="/marketplace" element={<NoAccessPage />} />
                    <Route path="/billing" element={<NoAccessPage />} />
                    <Route
                      path="/my-organization"
                      element={<MyOrganizationPage />}
                    />
                    <Route
                      path="/organization"
                      element={<OrganizationPage />}
                    />
                    <Route path="/thirdparty" element={<ThirdParty />} />
                    <Route
                      path="/:providerName/customerscopeselection"
                      element={<ScopeSelection />}
                    />
                    <Route path="/response/" element={<ResponsePage />} />
                    <Route path="/api-tokens" element={<ApiTokensPage />} />
                    <Route path="/debug" element={<DebugPage />} />
                    <Route
                      path="/error-history"
                      element={<ErrorHistoryPage />}
                    />
                  </Routes>
                )}
              </div>
              <SearchHintOverlay position="bottom-right" />
              <JumpBar />
            </div>
          </JumpBarProvider>
        </SelectedDeviceProvider>
      </SelectedTimeRangeProvider>
    </ErrorProvider>
  );
};

export default App;
