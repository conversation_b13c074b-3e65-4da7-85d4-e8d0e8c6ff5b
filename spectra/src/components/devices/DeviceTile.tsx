import type { Thing } from "api/ingestion/things";
import { useSelectedDevice } from "context/SelectedDeviceContext";
import { typeToLabel } from "utils/typeToLabel";
import { ComponentErrorBoundary } from "../uikit/ErrorBoundary/ComponentErrorBoundary";

import { ReactComponent as BatteriesIcon } from "images/icons/batteries.svg";
import { ReactComponent as ChargersIcon } from "images/icons/chargers.svg";
import { ReactComponent as MeterIcon } from "images/icons/meter.svg";
import { ReactComponent as NoConnectionIcon } from "images/icons/no-connection.svg";
import { ReactComponent as SwapStationIcon } from "images/icons/swap-station.svg";

import { useDeviceStats } from "../../hooks/useDeviceStats";

enum Color {
  Blue = "blue",
  Space = "space",
  Green = "green",
  Yellow = "yellow",
  Red = "red",
  Gray = "gray",
  Low = "low",
  Medium = "medium",
  High = "high",
  Disconnected = "disconnected",
}

const colorToTailwindText = (color: Color) => {
  switch (color) {
    case Color.Blue:
      return "text-blue20";
    case Color.Space:
      return "text-space20";
    case Color.Green:
      return "text-green20";
    case Color.Yellow:
      return "text-yellow20";
    case Color.Red:
      return "text-red20";
    case Color.Gray:
      return "text-gray20";
    case Color.Low:
      return "text-blue10";
    case Color.Medium:
      return "text-blue10";
    case Color.High:
      return "text-blue10";
    case Color.Disconnected:
      return "text-gray60";
  }
};

const colorToTailwindBackground = (color: Color) => {
  switch (color) {
    case Color.Blue:
      return "bg-blue80";
    case Color.Space:
      return "bg-space80";
    case Color.Green:
      return "bg-green80";
    case Color.Yellow:
      return "bg-yellow80";
    case Color.Red:
      return "bg-red80";
    case Color.Gray:
      return "bg-gray97";
    case Color.Low:
      return "bg-blue90";
    case Color.Medium:
      return "bg-blue80";
    case Color.High:
      return "bg-blue70";
    case Color.Disconnected:
      return "bg-gray90";
  }
};

const percentageToColor = (percentage: number) => {
  if (Number.isNaN(percentage)) {
    return Color.Gray;
  }

  if (percentage === 0) {
    return Color.Gray;
  }

  if (percentage < 0.2) {
    return Color.Low;
  }

  if (percentage < 0.5) {
    return Color.Medium;
  }

  return Color.High;
};

// this is a compressed version of the progress bar that is used in the table
const TableProgressBarInner = ({
  value,
  percentage,
  unit,
  label,
  digits = 0,
}: {
  value: number | string;
  percentage: number;
  unit: string;
  label: string;
  digits?: number;
}) => {
  const formattedValue =
    typeof value === "number"
      ? value.toLocaleString("en-US", {
          maximumFractionDigits: digits,
        })
      : value;

  const color = percentageToColor(percentage);
  return (
    <div className={`w-full ${colorToTailwindBackground(color)} rounded-sm`}>
      <div className="px-4 py-1 justify-between items-center flex gap-1 text-[10px]">
        <div className="capitalize">{label}</div>
        <div className={`${colorToTailwindText(color)} whitespace-nowrap`}>
          {formattedValue} {unit}
        </div>
      </div>
    </div>
  );
};

export const TableProgressBar = (props: {
  value: number | string;
  percentage: number;
  unit: string;
  label: string;
  digits?: number;
}) => (
  <ComponentErrorBoundary
    componentName="Table Progress Bar"
    onError={(error) => {
      console.error(`TableProgressBar error for ${props.label}:`, error);
    }}
    fallback={(error, retry) => (
      <div className="flex flex-col w-full items-center justify-center p-2 bg-gray-50 rounded-lg border border-gray-200">
        <div className="text-center">
          <h3 className="text-xs font-medium text-gray-900">
            Table Progress Bar Unavailable
          </h3>
        </div>
      </div>
    )}
  >
    <TableProgressBarInner {...props} />
  </ComponentErrorBoundary>
);

export const ProgressBar = ({
  value,
  percentage,
  unit,
  label,
  colorOverride = null,
  digits = 0,
}: {
  value: number | string;
  percentage: number;
  unit: string;
  label: string;
  colorOverride?: Color | null;
  digits?: number;
}) => {
  const formattedValue =
    typeof value === "number"
      ? value.toLocaleString("en-US", {
          maximumFractionDigits: digits,
        })
      : value;

  const color = colorOverride ?? percentageToColor(percentage);

  // HACK: make the tailwind compiler to recognize that we need to generate these colors
  const backgroundColor = colorToTailwindBackground(color);
  const textColor = colorToTailwindText(color);

  return (
    <div className="relative w-full">
      <div className="z-0 absolute top-0 left-0 bottom-0 right-0">
        <div className="w-full h-full flex">
          {percentage < 0 && <div className="flex-1" />}
          <div
            className={`h-full ${backgroundColor}`}
            style={{ width: `${Math.min(Math.abs(percentage), 1) * 100}%` }}
          />
        </div>
      </div>
      <div className="z-10 relative px-4 py-1 justify-between items-center flex">
        <div className={`${textColor} justify-start items-center gap-1 flex`}>
          <div className="text-body">{formattedValue}</div>
          <div className="text-[10px]">{unit}</div>
        </div>
        <div className="text-right text-[10px] capitalize">{label}</div>
      </div>
    </div>
  );
};

const Icon = ({ device }: { device: Thing }) => {
  switch (device.thingType) {
    case "Charger":
      return <ChargersIcon />;
    case "Battery":
      return <BatteriesIcon />;
    case "Meter":
      return <MeterIcon />;
    case "SwapStation":
      return <SwapStationIcon />;
    default:
      return null;
  }
};

const DeviceTileInner = ({ device }: { device: Thing }) => {
  const { selectedDevice, setSelectedDevice } = useSelectedDevice();
  const { stats, isLoading, isDisconnected } = useDeviceStats(device);

  const StatsSkeleton = () => (
    <div className="w-full pb-2 flex-col justify-start items-start gap-px flex">
      {[...Array(3)].map((_, index) => (
        // biome-ignore lint/suspicious/noArrayIndexKey: technically doesnt matter what order this comes out to
        <div key={index} className="relative w-full px-4 py-1">
          <div className="justify-start items-center gap-1 flex">
            <div className="h-5 bg-gray-200 w-16 animate-pulse" />
            <div className="h-4 bg-gray-200 w-8 animate-pulse" />
          </div>
        </div>
      ))}
    </div>
  );

  const header = (
    <div className="flex w-full px-2 pt-2 flex-col">
      <div className="flex justify-between items-center">
        <div className="flex gap-2 justify-start items-center">
          <Icon device={device} />
          <div className="shrink">
            <div className="text-footnote text-space50 break-words whitespace-normal max-w-[120px]">
              {device.thingName}
            </div>
            <div className="text-detail text-space70 break-words whitespace-normal max-w-[120px]">
              {device.thingType}
            </div>
          </div>
        </div>
        {isDisconnected && <NoConnectionIcon className="w-4 h-4" />}
      </div>
      {/* Status? */}
      {/* Alerts */}
    </div>
  );

  // TODO: figure out %
  const statsView = isLoading ? (
    <StatsSkeleton />
  ) : Object.values(stats).length > 0 ? (
    <div className="w-full pb-2 flex-col justify-start items-start gap-px flex">
      {Object.values(stats).map((stat) => {
        const numericValue = typeof stat.value === "number" ? stat.value : 0;
        return (
          <ProgressBar
            key={stat.type}
            value={stat.value ?? "-"}
            percentage={numericValue / 100}
            unit={stat.unit}
            label={typeToLabel(stat.type)}
            colorOverride={isDisconnected ? Color.Disconnected : undefined}
            digits={Math.abs(numericValue) < 1 ? 3 : 0} // HACK: this is a hack to get the digits to be correct for small values
          />
        );
      })}
    </div>
  ) : (
    <div
      className={`w-full pb-2 flex-col justify-start items-start gap-px flex ${
        isDisconnected ? "opacity-30" : ""
      }`}
    >
      <ProgressBar value={"-"} percentage={0} unit="" label="No data" />
    </div>
  );

  const selectCurrentDevice = () => {
    setSelectedDevice(
      selectedDevice && selectedDevice.thingId === device.thingId
        ? null
        : device,
    );
  };

  return (
    <div className="relative flex w-[200px]">
      <div
        className="bg-white rounded-md elevation-1 cursor-pointer hover:-translate-y-0.5 focus:translate-y-0.5 transition-colors transition-transform transition-shadow hover:shadow-md flex-col justify-start items-start gap-2 w-full flex"
        onClick={selectCurrentDevice}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            selectCurrentDevice();
          }
        }}
      >
        {header}
        {statsView}
      </div>
      {selectedDevice && selectedDevice.thingId === device.thingId && (
        <div className="absolute inline-flex h-full w-full animate-pulse rounded-md outline outline-2 outline-offset-2 outline-blue50" />
      )}
    </div>
  );
};

// Wrapped export with error boundary
export const DeviceTile = ({ device }: { device: Thing }) => (
  <ComponentErrorBoundary
    componentName="Device Tile"
    onError={(error) => {
      console.error(
        `DeviceTile error for device ${device.thingName || device.thingId}:`,
        error,
      );
    }}
  >
    <DeviceTileInner device={device} />
  </ComponentErrorBoundary>
);

export default DeviceTile;
