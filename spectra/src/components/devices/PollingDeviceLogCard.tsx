import { usePollingDeviceLogs } from "hooks/usePollingDeviceLogs";
import { FaPlay, FaStop } from "react-icons/fa";
import { formatLocalTime, formatUtcTime } from "utils/dateFormatters";
import { InsetToggle } from "../uikit/InsetToggle";
import ButtonComponent from "../uikit/button";
import LogsTable from "./LogsTable";
import PollingTimeseriesChart from "./PollingTimeseriesChart";

interface PollingDeviceLogCardProps {
  placeType: string;
  placeId: string;
  thingId: string;
  pollingIntervalMs?: number;
  minTimeDiffSeconds?: number;
}

const PollingDeviceLogCard = ({
  placeType,
  placeId,
  thingId,
  pollingIntervalMs,
  minTimeDiffSeconds,
}: PollingDeviceLogCardProps) => {
  const {
    isPolling,
    logs,
    lastFetched,
    isLoading,
    isLoadingOlderLogs,
    error,
    showUtcTimestamps,
    setShowUtcTimestamps,
    handleStartPolling,
    handleStopPolling,
    fetchOlderLogs,
    clearLogs,
  } = usePollingDeviceLogs({
    placeType,
    placeId,
    thingId,
    pollingIntervalMs,
    minTimeDiffSeconds,
  });

  return (
    <div className="p-4 pt-2 elevation-1 rounded-md flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div
            className={`w-2.5 h-2.5 rounded-full ${isPolling ? "bg-green50 animate-pulse" : "bg-gray95"}`}
          />
          <h3 className="text-heading3 text-space50">
            Live Device Events
            <div className="flex items-center gap-2 text-xs text-space70 font-mono">
              Last refresh:{" "}
              {!lastFetched
                ? "-"
                : showUtcTimestamps
                  ? formatUtcTime(lastFetched)
                  : formatLocalTime(lastFetched)}
            </div>
          </h3>
          <InsetToggle
            checkedLabel="UTC"
            uncheckedLabel="Local"
            checked={showUtcTimestamps}
            onChange={setShowUtcTimestamps}
          />
        </div>
        <div className="flex items-center gap-2">
          {!isPolling ? (
            <button
              onClick={handleStartPolling}
              className="px-3 py-1 text-blue50 text-sm rounded hover:text-blue70 transition-colors"
              type="button"
            >
              <FaPlay />
            </button>
          ) : (
            <button
              onClick={handleStopPolling}
              className="px-3 py-1 text-red50 text-sm rounded hover:text-red70 transition-colors"
              type="button"
            >
              <FaStop />
            </button>
          )}
        </div>
      </div>
      {error && (
        <div className="text-red50 text-sm p-2 bg-red90 rounded">{error}</div>
      )}
      {logs.length === 0 && !isLoading && !error && (
        <p className="text-caption text-center text-space70 py-4">
          {isPolling
            ? "Waiting for logs..."
            : "Click the play button to begin monitoring"}
        </p>
      )}
      {isLoading && logs.length === 0 && (
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600" />
          <span className="ml-2 text-sm text-space70">Loading...</span>
        </div>
      )}
      {logs.length > 0 && (
        <LogsTable logs={logs} showUtcTimestamps={showUtcTimestamps} />
      )}

      <div className="flex justify-center my-2 gap-2">
        {logs.length > 0 && (
          <ButtonComponent.Pill
            onClick={clearLogs}
            buttonStyle="default"
            variant="outline"
          >
            Clear Events
          </ButtonComponent.Pill>
        )}

        <ButtonComponent.Pill
          onClick={fetchOlderLogs}
          disabled={isLoadingOlderLogs}
          buttonStyle="default"
          variant="outline"
        >
          {isLoadingOlderLogs ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue50" />
              Loading older logs...
            </div>
          ) : (
            "Fetch Older Events"
          )}
        </ButtonComponent.Pill>
      </div>

      <h4 className="text-xs text-space50 font-medium">
        Live Device Event Timeseries (Local Time, Max 15 minutes ago)
      </h4>

      <PollingTimeseriesChart
        placeType={placeType}
        placeId={placeId}
        thingId={thingId}
      />
    </div>
  );
};

export default PollingDeviceLogCard;
