import {
  type DatapointMap,
  type Timeseries,
  convertToTimeseries,
  datapointsToMap,
  useDataApi,
} from "api/data";
import { type Thing, useThingsApi } from "api/ingestion/things";
import { MeterDetail, Pin } from "components";
import { DeviceAlerts } from "components/alerts/DeviceAlerts";
import MetricCard from "components/alerts/MetricCard";
import { RawChart } from "components/charts";
import { StackedChart } from "components/charts/StackedChart";
import { mapboxConfig } from "configs/mapbox";
import { useSelectedDevice } from "context/SelectedDeviceContext";
import { useSelectedSimulation } from "context/SelectedSimulationContext";
import { useSelectedTimeRange } from "context/SelectedTimeRangeContext";
import { typeToLabel } from "utils/typeToLabel";

import { useEffect, useState } from "react";
import { Map as MapboxMap, Marker } from "react-map-gl";

import { ReactComponent as ControlsIcon } from "images/icons/deviceDetailTabs/controls.svg";
import { ReactComponent as DetailsIcon } from "images/icons/deviceDetailTabs/details.svg";
import { ReactComponent as SecurityIcon } from "images/icons/deviceDetailTabs/security.svg";

import { useAuth } from "../../context/AuthContext";
import ControlsTab from "./ControlsTab";
import EditDeviceModal from "./EditDeviceModal";
import { FakeBatteryControlTab } from "./FakeBatteryControlTab";
import PollingDeviceLogCard from "./PollingDeviceLogCard";
import SecurityTab from "./SecurityTab";

enum TabType {
  DETAILS = "details",
  SECURITY = "security",
  CONTROLS = "controls",
}

interface DeviceDetailProps {
  selectedDevice: Thing;
  setSelectedDevice?: (device: Thing | null) => void;
  placeType: string;
  onDeviceUpdated?: (updatedDevice: Thing) => void;
  onDeviceDeleted?: (deletedDeviceId: string) => void;
}

const DeviceDetail = ({
  selectedDevice,
  setSelectedDevice = () => {},
  placeType,
  onDeviceUpdated,
  onDeviceDeleted,
}: DeviceDetailProps) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const isMeter = selectedDevice?.thingType === "Meter";
  const { getThingsFromPlace } = useThingsApi();

  const refreshDeviceData = async () => {
    try {
      const placeId = selectedDevice.siteId || selectedDevice.fleetId;
      const updatedDevice = await getThingsFromPlace(
        selectedDevice.placeType.toLowerCase(),
        placeId,
        selectedDevice.thingId,
      );

      setSelectedDevice(updatedDevice);

      if (onDeviceUpdated && updatedDevice) {
        await onDeviceUpdated(updatedDevice);
      }
    } catch (error) {
      console.error("Error refreshing device data:", error);
    }
  };

  return (
    <div
      className={`origin-right pr-6 min-h-screen overflow-y-auto bg-white lg:relative transition-transform drop-shadow-xl lg:drop-shadow-none ${
        selectedDevice
          ? "translate-x-0 lg:ml-4 px-2 right-0 scale-x-100 lg:w-1/3 min-w-[500px] z-20"
          : "pl-4 translate-x-full scale-x-0 w-0"
      }`}
    >
      {selectedDevice && (
        <>
          {!isMeter && (
            <ChargerOrBatteryDetails
              {...selectedDevice}
              placeType={placeType}
              setSelectedDevice={setSelectedDevice}
              onEditClick={() => setIsEditModalOpen(true)}
            />
          )}
          {isMeter && (
            <MeterDetail
              {...selectedDevice}
              placeType={placeType}
              setSelectedDevice={setSelectedDevice}
              onEditClick={() => setIsEditModalOpen(true)}
            />
          )}

          <EditDeviceModal
            isOpen={isEditModalOpen}
            onCloseEditModal={() => setIsEditModalOpen(false)}
            onDeviceEdited={refreshDeviceData}
            onDeviceDeleted={onDeviceDeleted}
            device={selectedDevice}
            placeType={selectedDevice.placeType}
            placeId={selectedDevice?.siteId || selectedDevice?.fleetId}
            onCloseDeviceDetail={() => setSelectedDevice(null)}
          />
        </>
      )}
    </div>
  );
};

const ChargerOrBatteryDetails = ({
  placeType,
  placeId,
  thingId,
  thingType,
  thingName,
  thingDescription,
  longitude,
  latitude,
  model,
  thingManufacturerId,
  setSelectedDevice,
  onEditClick,
}: {
  placeType: string;
  placeId: string;
  thingId: string;
  thingType: string;
  thingName: string;
  thingDescription: string;
  longitude: number;
  latitude: number;
  model: string;
  thingManufacturerId: string;
  setSelectedDevice: (device: Thing | null) => void;
  onEditClick: () => void;
}) => {
  const isCharger = thingType === "Charger"; // TODO: this should be more robust
  const isBattery = thingType === "Battery";
  const { start, end } = useSelectedTimeRange();
  const { user, permissions } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>(TabType.DETAILS);
  const { selectedDevice } = useSelectedDevice();
  const [stats, setStats] = useState<DatapointMap>({});
  const [isStatsLoading, setIsStatsLoading] = useState(true);
  const [timeseries, setTimeseries] = useState<Timeseries | null>(undefined);
  const [timeseriesForSignalStrength, setTimeseriesForSignalStrength] =
    useState<Timeseries | null>(undefined);
  const { simulationId } = useSelectedSimulation();
  const { getDataForThing, getSummaryForThing, getThingRecentData } =
    useDataApi();

  // biome-ignore lint/correctness/useExhaustiveDependencies: adding getThingRecentData and getSummaryForThing to the dependency array causes a re-render loop
  useEffect(() => {
    const fetchStats = async () => {
      setIsStatsLoading(true);
      try {
        let statsMap = {};
        if (isBattery) {
          const recentData = await getThingRecentData(
            placeType ?? "site",
            placeId,
            thingId,
            start,
            end,
            simulationId,
          );

          statsMap = recentData[0].dataPoints.reduce((acc, dataPoint) => {
            const validTypes = [
              "soc",
              "soh",
              "batChargeCurrent",
              "batVoltage",
              "batTemp",
              "bmsMinCellTemp",
              "bmsMaxCellTemp",
              "batteryCapacity",
              "batteryDischarge",
            ];

            if (validTypes.includes(dataPoint.type)) {
              acc[dataPoint.type] = {
                value: dataPoint.value,
                unit: dataPoint.unit,
              };
            }
            return acc;
          }, {});
        } else {
          const stats = await getSummaryForThing(
            placeType ?? "site",
            placeId,
            thingId,
            start,
            end,
            simulationId,
          );
          statsMap = datapointsToMap(stats);
        }
        setStats(statsMap || {});
      } catch (error) {
        console.error("Error fetching data:", error);
        setStats({});
      } finally {
        setIsStatsLoading(false);
      }
    };

    const fetchTimeseries = async () => {
      const timeseriesStart = end.subtract(1, "day"); // HACK: we only want to show recent data for the timeseries
      // const [binValue, binUnit] = generateIdealBinSize(timeseriesStart, end);
      const [binValue, binUnit] = [1, "s"];
      getDataForThing(
        placeType ?? "site",
        placeId,
        thingId,
        timeseriesStart,
        end,
        binUnit,
        binValue,
        simulationId,
      )
        .then(convertToTimeseries)
        .then(setTimeseries)
        .catch((e) => {
          setTimeseries(null);
          console.error(e);
        });
    };

    const fetchTimeseriesForSignalStrength = async () => {
      getDataForThing(
        placeType ?? "site",
        placeId,
        thingId,
        start,
        end,
        "m",
        5,
        simulationId,
      )
        .then(convertToTimeseries)
        .then(setTimeseriesForSignalStrength)
        .catch((e) => {
          setTimeseriesForSignalStrength(null);
          console.error(e);
        });
    };

    if (!user) return;

    fetchStats();
    fetchTimeseries();
    fetchTimeseriesForSignalStrength();
  }, [placeType, placeId, thingId, start, end, simulationId]);

  const closeButton = (
    <button
      onClick={() => setSelectedDevice(null)}
      onKeyUp={(event) => {
        if (event.key === "Enter" || event.key === " ") {
          setSelectedDevice(null);
        }
      }}
      type="button"
      tabIndex={0}
      className="absolute top-0 right-0 text-blue50 cursor-pointer"
    >
      &times;
    </button>
  );

  const statsView = (
    <div className="p-4 elevation-1 rounded-md flex flex-col gap-4">
      <h3 className="text-heading3 text-space50">Stats</h3>
      <div>
        {isStatsLoading ? (
          <p className="text-caption text-center text-space70 py-4">
            Loading...
          </p>
        ) : Object.entries(stats).length === 0 ? (
          <p className="text-caption text-center text-space70 py-4">
            No stats available
          </p>
        ) : (
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(stats).map(([key, stat]) => (
              <MetricCard
                key={key}
                metricCardStat={{
                  value:
                    typeof stat.value === "number"
                      ? stat.value?.toFixed(2)
                      : stat.value,
                  label: typeToLabel(key),
                  units: stat.unit,
                  trend: 0,
                  type: typeof stat.value,
                }}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );

  const alertsView = (
    <div className="p-4 elevation-1 rounded-md flex flex-col gap-4">
      <DeviceAlerts
        placeType={placeType}
        placeId={placeId}
        thingId={thingId}
        partnerId={user?.partnerId}
        start={start}
        end={end}
        simulationId={simulationId}
      />
    </div>
  );

  const map = (
    <div className="p-4 w-full elevation-1 rounded-md flex overflow-hidden h-[300px]">
      <MapboxMap
        key={`${longitude}-${latitude}`}
        initialViewState={{
          longitude,
          latitude,
          zoom: 12,
        }}
        viewState={{
          width: "100%",
          height: "100%",
        }}
        mapStyle="mapbox://styles/mapbox/light-v11"
        mapboxAccessToken={mapboxConfig.token}
      >
        <Marker longitude={longitude} latitude={latitude} anchor="center">
          <Pin color={"#5EA15F"} />
        </Marker>
      </MapboxMap>
    </div>
  );

  const deviceMetrics = (
    <div className="p-4 elevation-1 rounded-md flex flex-col gap-4">
      <h3 className="text-heading3 text-space50">
        Device Metrics (Past 24 Hours)
      </h3>
      <StackedChart timeseries={timeseries} colors={["#345C9B"]} />
    </div>
  );

  // Function to extract signal strength data only
  const filterSignalStrengthData = (data) => {
    const filteredValues = data?.values.filter(
      (reading) => "Signal" in reading && reading.Signal !== null,
    );

    if (filteredValues?.length > 0) {
      return {
        start: data?.start,
        end: data?.end,
        types: ["Signal"],
        units: ["dBm"],
        values: filteredValues.map((reading) => ({
          Signal: reading.Signal,
          time: reading.time,
        })),
        summary: {
          Signal: data.summary.Signal,
          time: data.summary.time,
        },
      };
    }
  };

  const signalStrengthData = filterSignalStrengthData(
    timeseriesForSignalStrength,
  );

  const signalStrengthChart = (
    <div className="p-4 elevation-1 rounded-md flex flex-col gap-4">
      <h3 className="text-heading3 text-space50">Signal Strength Chart</h3>
      <RawChart
        timeseries={signalStrengthData}
        colors={["#f2aa3c", "#ab5fb3"]}
      />
    </div>
  );

  const rawDetails = (
    <div className="p-4 elevation-1 rounded-md flex flex-col">
      <h3 className="text-heading3 text-space50 mb-4">Raw Details</h3>
      <p className="text-caption text-space70">
        <span className="capitalize">{placeType}</span>: {placeId}
      </p>
      <p className="text-caption text-space70">
        Location: {latitude}, {longitude}
      </p>
      <p className="text-caption text-space70">Type Name: {thingType}</p>
      <p className="text-caption text-space70">Name: {thingName}</p>
      <p className="text-caption text-space70">Model: {model}</p>
      <p className="text-caption text-space70">
        Manufacturer ID: {thingManufacturerId}
      </p>

      <p className="text-caption text-space70">ID: {thingId}</p>
    </div>
  );

  const renderActionButtons = () => {
    if (permissions.includes("write:ingest_things")) {
      return (
        <button
          onClick={onEditClick}
          className="px-3.5 py-2 rounded-full border border-space80 justify-start items-center gap-1 cursor-pointer hover:bg-gray95 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue90 focus:ring-blue90 flex self-start"
          type="button"
        >
          <div className="text-space70 text-xs font-medium leading-[14px]">
            Edit Device
          </div>
        </button>
      );
    }
    return null;
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case TabType.DETAILS:
        return (
          <div className="flex flex-col gap-4">
            {renderActionButtons()}
            <PollingDeviceLogCard
              key={`${placeType}-${placeId}-${thingId}`} // forces a re-render when the device changes
              placeType={placeType}
              placeId={placeId}
              thingId={thingId}
            />

            {statsView}
            {signalStrengthData && signalStrengthChart}
            {alertsView}

            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 elevation-1 rounded-md flex flex-col">
                <h3 className="text-heading3 text-space50 mb-4">Properties</h3>
                {Object.keys(selectedDevice.properties).length > 0 ? (
                  Object.entries(selectedDevice.properties).map(
                    ([key, value]) => (
                      <p key={key} className="text-caption text-space70">
                        {key}: {value}
                      </p>
                    ),
                  )
                ) : (
                  <p className="text-caption text-space70">
                    No properties configured
                  </p>
                )}
              </div>
              <div className="p-4 elevation-1 rounded-md flex flex-col">
                <h3 className="text-heading3 text-space50 mb-4">Attributes</h3>
                {Object.keys(selectedDevice.attributes).length > 0 ? (
                  Object.entries(selectedDevice.attributes).map(
                    ([key, value]) => (
                      <p key={key} className="text-caption text-space70">
                        {key}: {value}
                      </p>
                    ),
                  )
                ) : (
                  <p className="text-caption text-space70">
                    No attributes configured
                  </p>
                )}
              </div>
            </div>
            {map}
            {deviceMetrics}
            {rawDetails}
          </div>
        );
      case TabType.SECURITY:
        return (
          <SecurityTab
            placeType={placeType}
            siteId={placeId}
            thingId={thingId}
          />
        );
      case TabType.CONTROLS:
        if (thingType === "SwapStation") {
          return (
            <ControlsTab
              placeType={placeType}
              siteId={placeId}
              thingId={thingId}
              thingManufacturerId={thingManufacturerId}
            />
          );
        }
        if (thingType === "Battery") {
          return <FakeBatteryControlTab />;
        }
        return (
          <div className="p-4 text-gray-500">
            Controls are not supported for this device.
          </div>
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    return () => {
      setActiveTab(TabType.DETAILS); // Reset to details tab when unmounting
    };
  }, []);

  return (
    <div className="flex flex-col gap-4 py-4">
      <div className="relative">
        <div className="items-center gap-2 text-gray-500">
          <p className="text-xs uppercase">{placeType}</p>
          <h4 className="text-lg font-medium text-gray-900">
            <span className="text-gray-500">{thingType}</span> / {thingName}
          </h4>
          {thingDescription && (
            <p className="text-sm text-gray-600 mt-1">{thingDescription}</p>
          )}
        </div>
        {closeButton}
      </div>

      <div className="rounded-lg p-1">
        <nav className="grid grid-cols-3 gap-1">
          <button
            onClick={() => setActiveTab(TabType.DETAILS)}
            type="button"
            className={`flex flex-col items-center py-3 px-2 rounded-lg transition-colors ${
              activeTab === TabType.DETAILS
                ? "bg-slate-200 text-slate-600"
                : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            }`}
          >
            <DetailsIcon
              className={`w-5 h-5 mb-1 ${
                activeTab === TabType.DETAILS
                  ? "text-blue-600"
                  : "text-gray-500"
              }`}
            />
            <span className="text-sm">Details</span>
          </button>
          <button
            onClick={() => setActiveTab(TabType.SECURITY)}
            type="button"
            className={`flex flex-col items-center py-3 px-2 rounded-lg transition-colors ${
              activeTab === TabType.SECURITY
                ? "bg-slate-200 text-slate-600"
                : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            }`}
          >
            <SecurityIcon
              className={`w-5 h-5 mb-1 ${
                activeTab === TabType.SECURITY
                  ? "text-blue-600"
                  : "text-gray-500"
              }`}
            />
            <span className="text-sm">Security</span>
          </button>
          <button
            onClick={() => setActiveTab(TabType.CONTROLS)}
            type="button"
            className={`flex flex-col items-center py-3 px-2 rounded-lg transition-colors ${
              activeTab === TabType.CONTROLS
                ? "bg-slate-200 text-slate-600"
                : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            }`}
          >
            <ControlsIcon
              className={`w-5 h-5 mb-1 ${
                activeTab === TabType.CONTROLS
                  ? "text-blue-600"
                  : "text-gray-500"
              }`}
            />
            <span className="text-sm">Controls</span>
          </button>
        </nav>
      </div>

      <div className="mt-4">{renderTabContent()}</div>
    </div>
  );
};

export default DeviceDetail;
