// this is a chart that shows a timeseries of a few device statistics that hooks onto the polling device logs time hook
// uses the useLastPolledTimeStore hook to get the start time and will fetch the data until lastEventTime using getThingRecentData

import { type Timeseries, convertToTimeseries, useDataApi } from "api/data";
import { StackedChart } from "components/charts/StackedChart";
import { useEffect, useState } from "react";
import { useLastPolledTimeStore } from "stores/lastPolledTimeStore";
import { type Dayjs, dayjs } from "utils/dayjs";

const usePollingTimeseriesChart = ({
  placeType,
  placeId,
  thingId,
}: {
  placeType: string;
  placeId: string;
  thingId: string;
}) => {
  const { currentPolledDeviceStartTime } = useLastPolledTimeStore();
  const lastEventTime = useLastPolledTimeStore(
    (state) => state.lastEventTime[thingId],
  );

  const { getThingRecentData } = useDataApi();
  const [timeseries, setTimeseries] = useState<Timeseries | null>(null);
  const [error, setError] = useState<string | null>(null);

  // biome-ignore lint/correctness/useExhaustiveDependencies: lastEventTime is a dependency of this hook because we want to refetch the data whenever we know there's new data and getThingRecentData is not a dependency of this hook
  useEffect(() => {
    let startTime: Dayjs | undefined = undefined;

    if (currentPolledDeviceStartTime) {
      // cap start time to 15 minutes ago
      const fifteenMinutesAgo = dayjs().subtract(15, "minutes");
      startTime = currentPolledDeviceStartTime.isAfter(fifteenMinutesAgo)
        ? currentPolledDeviceStartTime
        : fifteenMinutesAgo;
    }

    getThingRecentData(placeType, placeId, thingId, startTime)
      .then(convertToTimeseries)
      .then(setTimeseries)
      .catch((e) => {
        console.error(e);
        setError(e instanceof Error ? e.message : "Failed to fetch timeseries");
      });
  }, [
    currentPolledDeviceStartTime,
    lastEventTime,
    placeType,
    placeId,
    thingId,
  ]);

  return { timeseries, error };
};

const PollingTimeseriesChart = ({
  placeType,
  placeId,
  thingId,
}: {
  placeType: string;
  placeId: string;
  thingId: string;
}) => {
  const { timeseries, error } = usePollingTimeseriesChart({
    placeType,
    placeId,
    thingId,
  });

  if (error) {
    return <p className="text-space70 text-caption">{error}</p>;
  }

  if (!timeseries) {
    return <p className="text-space70 text-caption">Waiting for events...</p>;
  }

  return <StackedChart timeseries={timeseries} colors={["#345C9B"]} />;
};

export default PollingTimeseriesChart;
