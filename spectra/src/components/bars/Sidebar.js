import { useAuth } from "context/AuthContext";

import { FaSignOutAlt } from "react-icons/fa";
import { NavLink, useNavigate } from "react-router-dom";

import Badge from "components/uikit/Badge";
import NavItem from "components/uikit/NavItem";
import Spinner from "components/uikit/Spinner";
import { useOrganization } from "context/OrganizationContext";
import { ReactComponent as AlertsIcon } from "images/icons/alerts.svg";
import { ReactComponent as BillingIcon } from "images/icons/billing.svg";
import { ReactComponent as DevicesIcon } from "images/icons/devices.svg";
import { ReactComponent as IntegrationsIcon } from "images/icons/integrations.svg";
import { ReactComponent as KeyIcon } from "images/icons/key.svg";
import { ReactComponent as SettingsIcon } from "images/icons/settings.svg";
import { ReactComponent as SitesIcon } from "images/icons/sites.svg";
import { ReactComponent as UsersIcon } from "images/icons/users.svg";

import { useUnreadErrorCount } from "../../stores/errorStore";

const Sidebar = () => {
  const { user, logout } = useAuth();
  const { organization, brandingConfig, isLoading } = useOrganization();
  const navigate = useNavigate();
  const unreadErrorCount = useUnreadErrorCount();

  const logoUrl = brandingConfig?.logoUrl ?? require("images/sidebar_logo.png");

  return (
    <nav className="flex-none self-start sticky top-0">
      <div className="flex flex-col min-h-screen justify-between w-250">
        <div>
          <header className="p-5">
            {isLoading ? (
              <Spinner />
            ) : (
              <img
                src={logoUrl}
                alt={`${organization?.displayName} logo`}
                onClick={() => navigate("/")}
                className="cursor-pointer h-[30px]"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    navigate("/");
                  }
                }}
              />
            )}
          </header>
          <ul className="px-2">
            <NavLink to="/dashboard">
              {({ isActive }) => (
                <NavItem
                  title="Overview"
                  icon={
                    <IntegrationsIcon className="align-text-bottom mr-2 inline" />
                  }
                  selected={isActive}
                />
              )}
            </NavLink>
            <NavLink to="/sites">
              {({ isActive }) => (
                <NavItem
                  title="Sites"
                  icon={<SitesIcon className="align-text-bottom mr-2 inline" />}
                  selected={isActive}
                />
              )}
            </NavLink>
            <NavLink to="/fleets">
              {({ isActive }) => (
                <NavItem
                  title="Fleets"
                  icon={<SitesIcon className="align-text-bottom mr-2 inline" />}
                  selected={isActive}
                />
              )}
            </NavLink>
            <NavLink to="/devices">
              {({ isActive }) => (
                <NavItem
                  title="Devices"
                  icon={
                    <DevicesIcon className="align-text-bottom mr-2 inline" />
                  }
                  selected={isActive}
                />
              )}
            </NavLink>
            {/* <NavLink to="/deviceList">
              {({ isActive }) => (
                <NavItem
                  title="Devices"
                  icon={<DevicesIcon className="align-text-bottom mr-2 inline" />}
                  selected={isActive}
                />
            )}
            </NavLink> */}
            <NavLink to="/transaction">
              {({ isActive }) => (
                <NavItem
                  title="Finance"
                  icon={
                    <BillingIcon className="align-text-bottom mr-2 inline" />
                  }
                  selected={isActive}
                />
              )}
            </NavLink>
            {/* <NavLink to="/thirdparty">
              {({ isActive }) => ( */}
            {/* <NavItem
              enabled={false}
              title="Integrations"
              icon={
                <IntegrationsIcon className="align-text-bottom mr-2 inline" />
              }
              selected={isActive}
            /> */}
            {/* )}
            </NavLink> */}
            {/* <NavLink to="/marketplace">
              {({ isActive }) => ( */}
            {/* <NavItem
              enabled={false}
              title="Marketplace"
              icon={
                <MarketplaceIcon className="align-text-bottom mr-2 inline" />
              }
              selected={isActive}
            /> */}
            {/* )}
            </NavLink> */}
            {/* <NavLink to="/billing">
              {({ isActive }) => ( */}
            {/* <NavItem
              enabled={false}
              title="Billing"
              icon={<BillingIcon className="align-text-bottom mr-2 inline" />}
              selected={isActive}
            /> */}
            {/* )}
            </NavLink> */}
            <NavLink to="/monitors">
              {({ isActive }) => (
                <NavItem
                  title="Monitors"
                  icon={
                    <AlertsIcon className="align-text-bottom mr-2 inline" />
                  }
                  selected={isActive}
                  beta={true}
                />
              )}
            </NavLink>
            <NavLink to="/integrations">
              {({ isActive }) => (
                <NavItem
                  title="Integrations"
                  icon={
                    <IntegrationsIcon className="align-text-bottom mr-2 inline" />
                  }
                  selected={isActive}
                  beta={true}
                />
              )}
            </NavLink>
            <NavLink to="/organization">
              {({ isActive }) => (
                <NavItem
                  title="Organization"
                  icon={<UsersIcon className="align-text-bottom mr-2 inline" />}
                  selected={isActive}
                />
              )}
            </NavLink>
            {/* <NavLink to="/my-organization">
              {({ isActive }) => (
                <NavItem
                  title="My organization"
                  icon={<UsersIcon className="align-text-bottom mr-2 inline" />}
                  selected={isActive}
                  beta={true}
                />
              )}
            </NavLink> */}
            <NavLink to="/api-tokens">
              {({ isActive }) => (
                <NavItem
                  title="API Tokens"
                  icon={<KeyIcon className="align-text-bottom mr-2 inline" />}
                  selected={isActive}
                />
              )}
            </NavLink>
            <NavLink to="/settings">
              {({ isActive }) => (
                <NavItem
                  title="Settings"
                  icon={
                    <SettingsIcon className="align-text-bottom mr-2 inline" />
                  }
                  selected={isActive}
                />
              )}
            </NavLink>
          </ul>
        </div>
        <footer className="p-8 flex flex-col gap-6">
          <div className="flex flex-col gap-4">
            <ul>
              {user.name?.includes("@aerovy.com") && (
                <>
                  <li className="text-caption my-2">
                    <NavLink
                      to="/react-query-demo"
                      className={({ isActive }) =>
                        `text-caption hover:text-blue30 transition-colors ${
                          isActive ? "text-blue30" : "text-blue50"
                        }`
                      }
                    >
                      🚀 React Query Demo
                    </NavLink>
                  </li>
                  <li className="text-caption my-2">
                    <NavLink
                      to="/error-history"
                      className={({ isActive }) =>
                        `text-caption hover:text-blue30 transition-colors ${
                          isActive ? "text-blue30" : "text-blue50"
                        }`
                      }
                    >
                      Error History
                      <Badge count={unreadErrorCount} />
                    </NavLink>
                  </li>
                </>
              )}
              <li className="text-caption my-2 text-blue50">
                API Documentation
              </li>
              <li className="text-caption my-2 text-blue50">
                <NavLink
                  to="http://aerovy.com/general-terms-of-use"
                  className="text-blue50 hover:text-blue30 transition-colors"
                >
                  Terms of Use
                </NavLink>
              </li>
              <li className="text-caption my-2 text-blue50">Help</li>
            </ul>
            <p className="text-caption">&copy; 2025</p>
          </div>
          <hr className="border-gray95" />
          <div className="flex flex-row gap-2" title={user.name}>
            <img
              src={user.picture}
              alt="Profile"
              className="w-8 h-8 rounded-full bg-gray95"
            />
            <div className="truncate">
              <p className="text-caption text-space50 truncate">
                {user.nickname}
              </p>
              <p className="text-caption mt-0 text-space70 truncate">
                {user.name} testing
              </p>
            </div>
            <button
              className="text-caption text-red50"
              onClick={logout}
              type="button"
            >
              <FaSignOutAlt />
            </button>
          </div>
        </footer>
      </div>
    </nav>
  );
};

export default Sidebar;
