/**
 * ErrorProvider component for managing and displaying errors globally
 */

import React, { useEffect, useRef, useCallback } from "react";
import { useLocation } from "react-router-dom";
import { errorHistoryService } from "../../../services/errorHistoryService";
import {
  useErrorActions,
  useErrors,
  useSilentMode,
} from "../../../stores/errorStore";
import { ErrorBoundary } from "../ErrorBoundary/ErrorBoundary";
import { ErrorToast } from "../ErrorToast/ErrorToast";

interface ErrorProviderProps {
  children: React.ReactNode;
  maxToasts?: number;
  toastPosition?: "top-right" | "top-left" | "bottom-right" | "bottom-left";
  enableErrorBoundary?: boolean;
  className?: string;
}

export const ErrorProvider: React.FC<ErrorProviderProps> = ({
  children,
  maxToasts = 5,
  toastPosition = "top-right",
  enableErrorBoundary = true,
  className = "",
}) => {
  const errors = useErrors();
  const location = useLocation();
  const { clearErrors, clearNoAccessError, incrementUnreadCount } =
    useErrorActions();
  const silentMode = useSilentMode();
  const isInitialMount = useRef(true);

  // Create stable callback for clearing errors
  const clearAllErrors = useCallback(() => {
    clearErrors();
    clearNoAccessError();
  }, [clearErrors, clearNoAccessError]);

  // Clear errors when route changes to provide fresh error state for each route
  // biome-ignore lint/correctness/useExhaustiveDependencies: we need to trigger this when changing route to reset error overlay
  useEffect(() => {
    // Skip clearing errors on initial mount
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Clear all errors and no-access error state when navigating to a new route
    // This ensures users don't see stale error overlays when navigating to new routes
    clearAllErrors();
  }, [location.pathname, clearAllErrors]);

  // Filter errors that should be displayed as toasts
  const toastErrors = errors
    .filter((error) => error.severity !== "critical") // Critical errors might need different handling
    .slice(0, maxToasts); // Limit number of toasts

  // Track errors in history when they are displayed as toasts
  const trackedErrorIds = useRef(new Set<string>());

  // Map ErrorCategory to error history service types
  const mapErrorType = useCallback(
    (
      category: string,
    ): "network" | "validation" | "auth" | "server" | "unknown" => {
      switch (category) {
        case "network":
          return "network";
        case "validation":
          return "validation";
        case "authentication":
        case "authorization":
          return "auth";
        case "server":
          return "server";
        default:
          return "unknown";
      }
    },
    [],
  );

  useEffect(() => {
    const trackErrors = async () => {
      for (const error of toastErrors) {
        // Only track each error once
        if (!trackedErrorIds.current.has(error.id)) {
          trackedErrorIds.current.add(error.id);

          try {
            // Check if error has stack property (for runtime errors)
            const runtimeError = error as unknown as Record<string, unknown>;
            const stack = runtimeError.stack || runtimeError.componentStack;

            await errorHistoryService.addError({
              message: error.message,
              type: mapErrorType(error.category),
              severity: error.severity,
              timestamp: error.timestamp,
              source: error.title,
              stack: typeof stack === "string" ? stack : undefined,
              url: window.location.href,
              metadata: {
                errorId: error.id,
                title: error.title,
                category: error.category,
                userAgent: navigator.userAgent,
                details: error.details,
                actions: error.actions?.map((action) => ({
                  label: action.label,
                  variant: action.variant,
                })),
                dismissible: error.dismissible,
                autoHide: error.autoHide,
                retryable: error.retryable,
              },
            });

            // Increment unread count for new errors
            incrementUnreadCount();
          } catch (historyError) {
            console.warn("Failed to save error to history:", historyError);
          }
        }
      }
    };

    trackErrors();
  }, [toastErrors, mapErrorType, incrementUnreadCount]);

  const content = (
    <div className={className}>
      {children}

      {/* Error Toasts */}
      {!silentMode && (
        <div className="error-toasts">
          {toastErrors.map((error, index) => (
            <ErrorToast
              key={error.id}
              error={error}
              position={toastPosition}
              style={getToastSpacing(index, toastPosition)}
            />
          ))}
        </div>
      )}
    </div>
  );

  // Wrap with ErrorBoundary if enabled
  if (enableErrorBoundary) {
    return <ErrorBoundary showErrorInStore={true}>{content}</ErrorBoundary>;
  }

  return content;
};

/**
 * Calculate spacing for stacked toasts
 */
function getToastSpacing(index: number, position: string): React.CSSProperties {
  const spacing = index * 80; // 80px spacing between toasts

  if (position.includes("top")) {
    return { transform: `translateY(${spacing}px)` };
  }
  return { transform: `translateY(-${spacing}px)` };
}

/**
 * Hook for managing error provider configuration
 */
export const useErrorProvider = () => {
  const [config, setConfig] = React.useState({
    maxToasts: 5,
    toastPosition: "top-right" as const,
    enableErrorBoundary: true,
  });

  const updateConfig = React.useCallback(
    (newConfig: Partial<typeof config>) => {
      setConfig((prev) => ({ ...prev, ...newConfig }));
    },
    [],
  );

  return { config, updateConfig };
};
