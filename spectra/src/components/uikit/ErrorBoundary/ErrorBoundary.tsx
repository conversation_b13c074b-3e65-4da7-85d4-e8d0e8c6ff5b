/**
 * ErrorBoundary component for catching React component errors
 */

import React, { Component, type ReactNode } from "react";
import { useErrorStore } from "../../../stores/errorStore";
import type { AppError } from "../../../types/errors";
import { createRuntimeError } from "../../../utils/errorUtils";

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: AppError, retry: () => void) => ReactNode;
  onError?: (error: AppError) => void;
  showErrorInStore?: boolean;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: AppError | null;
}

export class ErrorBoundary extends Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    const appError = createRuntimeError(error);
    return {
      hasError: true,
      error: appError,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const appError = createRuntimeError(error, errorInfo.componentStack);

    // Update state with the error
    this.setState({ error: appError });

    // Add to error store if enabled
    if (this.props.showErrorInStore !== false) {
      useErrorStore.getState().addError(appError);
    }

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(appError);
    }

    // Log to console for debugging
    console.error("ErrorBoundary caught an error:", error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Default fallback UI
      return (
        <DefaultErrorFallback
          error={this.state.error}
          onRetry={this.handleRetry}
        />
      );
    }

    return this.props.children;
  }
}

export interface DefaultErrorFallbackProps {
  error: AppError;
  onRetry: () => void;
}

export const DefaultErrorFallback: React.FC<DefaultErrorFallbackProps> = ({
  error,
  onRetry,
}) => {
  return (
    <div className="flex flex-col w-full items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-200 min-h-[120px] max-h-[200px]">
      <div className="text-center">
        <h3 className="text-sm font-medium text-gray-900 mb-1">
          {error?.title || "Component Unavailable"}
        </h3>
        <p className="text-xs text-gray-600 mb-3">
          {error?.message ||
            "Unable to display component. Please try refreshing."}
        </p>
        <button
          type="button"
          onClick={onRetry}
          className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
        >
          Try Again
        </button>

        {process.env.NODE_ENV === "development" &&
          (error as { stack?: string })?.stack && (
            <details className="mt-3 text-left">
              <summary className="cursor-pointer text-xs text-gray-500 hover:text-gray-700">
                Show Error Details
              </summary>
              <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-24 text-left">
                {(error as { stack?: string }).stack}
              </pre>
            </details>
          )}
      </div>
    </div>
  );
};

// Hook version for functional components
export const useErrorBoundary = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const captureError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { captureError, resetError };
};
