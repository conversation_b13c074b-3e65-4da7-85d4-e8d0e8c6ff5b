/**
 * ChartErrorBoundary - Specialized error boundary for chart components
 * Shows user-friendly error messages instead of crashing the entire page
 */

import type React from "react";
import { Component, type ReactNode } from "react";
import { useErrorStore } from "../../../stores/errorStore";
import type { AppError } from "../../../types/errors";
import { createError } from "../../../utils/errorUtils";
import ButtonComponent from "../button";

interface ChartErrorBoundaryProps {
  children: ReactNode;
  chartName?: string;
  fallback?: (error: AppError, retry: () => void) => ReactNode;
  onError?: (error: AppError) => void;
}

interface ChartErrorBoundaryState {
  hasError: boolean;
  error: AppError | null;
}

/**
 * Error boundary specifically designed for chart components
 * Creates "high" severity errors instead of "critical" so they show as dismissible toasts
 */
export class ChartErrorBoundary extends Component<
  ChartErrorBoundaryProps,
  ChartErrorBoundaryState
> {
  constructor(props: ChartErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ChartErrorBoundaryState {
    return { hasError: true, error: null };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const chartName = this.props.chartName || "Chart";

    // Create a high-severity error (not critical) so it shows as a toast
    const appError = createError(
      `${chartName} Error`,
      `Unable to display ${chartName.toLowerCase()}. ${error.message}`,
      "runtime",
      {
        severity: "high", // Use "high" instead of "critical" for toasts
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        dismissible: true,
        autoHide: false, // Don't auto-hide chart errors
      },
    );

    // Update state with the error
    this.setState({ error: appError });

    // Add to error store for toast display
    useErrorStore.getState().addError(appError);

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(appError);
    }

    // Log to console for debugging
    console.error(
      `ChartErrorBoundary caught an error in ${chartName}:`,
      error,
      errorInfo,
    );
  }

  handleRetry = (event?: React.MouseEvent) => {
    // Prevent event bubbling to parent components
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Default fallback UI for charts
      const chartName = this.props.chartName || "Chart";
      return (
        <div className="flex flex-col w-full items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-200">
          <div className="text-center">
            <h3 className="text-sm font-medium text-gray-900 mb-1">
              {chartName} Unavailable
            </h3>
            <p className="text-xs text-gray-600 mb-3">
              Unable to display chart data. Please try refreshing the page.
            </p>
            <ButtonComponent.Menu
              className="text-center justify-center"
              onClick={this.handleRetry}
              type="button"
            >
              Try Again
            </ButtonComponent.Menu>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook version for functional components
 */
export const useChartErrorBoundary = () => {
  const addError = useErrorStore((state) => state.addError);

  const reportChartError = (error: Error, chartName = "Chart") => {
    const appError = createError(
      `${chartName} Error`,
      `Unable to display ${chartName.toLowerCase()}. ${error.message}`,
      "runtime",
      {
        severity: "high",
        stack: error.stack,
        dismissible: true,
        autoHide: false,
      },
    );

    addError(appError);
    console.error(`Chart error in ${chartName}:`, error);
  };

  return { reportChartError };
};
