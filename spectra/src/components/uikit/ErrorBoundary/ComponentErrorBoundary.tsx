/**
 * ComponentErrorBoundary - Specialized error boundary for components
 */

import type React from "react";
import { Component, type ReactNode } from "react";
import { useErrorStore } from "../../../stores/errorStore";
import type { AppError } from "../../../types/errors";
import { createError } from "../../../utils/errorUtils";
import ButtonComponent from "../button";

interface ComponentErrorBoundaryProps {
  children: ReactNode;
  componentName?: string;
  fallback?: (error: AppError, retry: () => void) => ReactNode;
  onError?: (error: AppError) => void;
}

interface ComponentErrorBoundaryState {
  hasError: boolean;
  error: AppError | null;
}

export class ComponentErrorBoundary extends Component<
  ComponentErrorBoundaryProps,
  ComponentErrorBoundaryState
> {
  constructor(props: ComponentErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ComponentErrorBoundaryState {
    return { hasError: true, error: null };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const componentName = this.props.componentName || "Chart";

    // Create a high-severity error (not critical) so it shows as a toast
    const appError = createError(
      `${componentName} Error`,
      `Unable to display ${componentName.toLowerCase()}. ${error.message}`,
      "runtime",
      {
        severity: "high", // Use "high" instead of "critical" for toasts
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        dismissible: true,
        autoHide: false, // Don't auto-hide component errors
      },
    );

    // Update state with the error
    this.setState({ error: appError });

    // Add to error store for toast display
    useErrorStore.getState().addError(appError);

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(appError);
    }

    // Log to console for debugging
    console.error(
      `ComponentErrorBoundary caught an error in ${componentName}:`,
      error,
      errorInfo,
    );
  }

  handleRetry = (event?: React.MouseEvent) => {
    // Prevent event bubbling to parent components
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Default fallback UI for components
      const componentName = this.props.componentName || "Chart";
      return (
        <div className="flex flex-col w-full items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-200">
          <div className="text-center">
            <h3 className="text-sm font-medium text-gray-900 mb-1">
              {componentName} Unavailable
            </h3>
            <p className="text-xs text-gray-600 mb-3">
              Unable to display component data. Please try refreshing the page.
            </p>
            <ButtonComponent.Menu
              className="text-center justify-center"
              onClick={this.handleRetry}
              type="button"
            >
              Try Again
            </ButtonComponent.Menu>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook version for functional components
 */
export const useComponentErrorBoundary = () => {
  const addError = useErrorStore((state) => state.addError);

  const reportComponentError = (error: Error, componentName = "Component") => {
    const appError = createError(
      `${componentName} Error`,
      `Unable to display ${componentName.toLowerCase()}. ${error.message}`,
      "runtime",
      {
        severity: "high",
        stack: error.stack,
        dismissible: true,
        autoHide: false,
      },
    );

    addError(appError);
    console.error(`Component error in ${componentName}:`, error);
  };

  return { reportComponentError };
};
