/**
 * Storybook stories for ErrorBoundary component
 */

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import React from "react";
import { ErrorBoundary } from "./ErrorBoundary";

const meta: Meta<typeof ErrorBoundary> = {
  title: "UIKit/ErrorBoundary",
  component: ErrorBoundary,
  parameters: {
    layout: "padded",
  },
};

export default meta;
type Story = StoryObj<typeof ErrorBoundary>;

// Component that throws an error
const ThrowError: React.FC<{ shouldThrow?: boolean; message?: string }> = ({
  shouldThrow = true,
  message = "This is a test error",
}) => {
  if (shouldThrow) {
    throw new Error(message);
  }
  return (
    <div className="p-4 bg-green-100 text-green-800 rounded">
      Component rendered successfully!
    </div>
  );
};

// Component that works normally
const WorkingComponent: React.FC = () => {
  return (
    <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
      <h3 className="text-lg font-semibold text-blue-800 mb-2">
        Working Component
      </h3>
      <p className="text-blue-700">
        This component renders without any errors.
      </p>
      <button
        type="button"
        className="mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        Click me
      </button>
    </div>
  );
};

export const Default: Story = {
  render: () => (
    <ErrorBoundary>
      <WorkingComponent />
    </ErrorBoundary>
  ),
};

export const WithError: Story = {
  render: () => (
    <ErrorBoundary>
      <ThrowError />
    </ErrorBoundary>
  ),
};

export const WithCustomError: Story = {
  render: () => (
    <ErrorBoundary>
      <ThrowError message="Custom error message for testing" />
    </ErrorBoundary>
  ),
};

export const WithCustomFallback: Story = {
  render: () => (
    <ErrorBoundary
      fallback={(error, retry) => (
        <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg text-center">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">
            Custom Error Fallback
          </h3>
          <p className="text-yellow-700 mb-4">{error.message}</p>
          <button
            type="button"
            onClick={retry}
            className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
          >
            Custom Retry Button
          </button>
        </div>
      )}
    >
      <ThrowError message="Error with custom fallback" />
    </ErrorBoundary>
  ),
};

export const WithErrorHandler: Story = {
  render: () => (
    <ErrorBoundary
      onError={(error) => {
        console.log("Custom error handler called:", error);
        alert(`Error caught: ${error.message}`);
      }}
    >
      <ThrowError message="Error with custom handler" />
    </ErrorBoundary>
  ),
};

export const NestedComponents: Story = {
  render: () => (
    <ErrorBoundary>
      <div className="space-y-4">
        <WorkingComponent />
        <ErrorBoundary>
          <ThrowError message="Nested error boundary test" />
        </ErrorBoundary>
        <WorkingComponent />
      </div>
    </ErrorBoundary>
  ),
};

export const ConditionalError: Story = {
  render: () => {
    const [shouldThrow, setShouldThrow] = React.useState(false);

    return (
      <div className="space-y-4">
        <button
          type="button"
          onClick={() => setShouldThrow(!shouldThrow)}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
        >
          {shouldThrow ? "Fix Component" : "Break Component"}
        </button>

        <ErrorBoundary>
          <ThrowError
            shouldThrow={shouldThrow}
            message="Conditional error test"
          />
        </ErrorBoundary>
      </div>
    );
  },
};

export const MultipleErrors: Story = {
  render: () => (
    <div className="space-y-4">
      <ErrorBoundary>
        <ThrowError message="First error boundary" />
      </ErrorBoundary>

      <ErrorBoundary>
        <ThrowError message="Second error boundary" />
      </ErrorBoundary>

      <ErrorBoundary>
        <WorkingComponent />
      </ErrorBoundary>
    </div>
  ),
};

// Mock components
const MockPlaceTile: React.FC<{ shouldFail?: boolean }> = ({
  shouldFail = false,
}) => {
  if (shouldFail) {
    throw new Error("Failed to load place data");
  }
  return (
    <div className="w-full mb-4 bg-white rounded-md shadow border border-zinc-300 p-4">
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center gap-2">
          <div className="px-2 py-1 bg-gray-100 rounded text-sm font-medium">
            SIT
          </div>
          <div>
            <div className="text-sm font-normal">Sample Site</div>
            <div className="text-xs text-gray-600">123 Main St</div>
          </div>
        </div>
        <div className="text-sm">3 🔋 2 ⚡ 1 📊</div>
      </div>
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <div className="text-xl font-medium">
            150 <span className="text-xs">kWh</span>
          </div>
          <div className="text-xs text-gray-600">Currently Stored</div>
        </div>
        <div>
          <div className="text-xl font-medium">
            75 <span className="text-xs">kWh</span>
          </div>
          <div className="text-xs text-gray-600">Drawn from Grid</div>
        </div>
      </div>
    </div>
  );
};

const MockDeviceTile: React.FC<{ shouldFail?: boolean }> = ({
  shouldFail = false,
}) => {
  if (shouldFail) {
    throw new Error("Failed to load device stats");
  }
  return (
    <div className="w-[200px] bg-white rounded-md shadow border p-3">
      <div className="flex items-center gap-2 mb-2">
        <div className="w-6 h-6 bg-blue-500 rounded" />
        <div>
          <div className="text-sm font-medium">Battery 01</div>
          <div className="text-xs text-gray-600">Battery</div>
        </div>
      </div>
      <div className="space-y-1">
        <div className="flex justify-between text-xs">
          <span>Power</span>
          <span>85%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-green-500 h-2 rounded-full"
            style={{ width: "85%" }}
          />
        </div>
      </div>
    </div>
  );
};

const MockSummaryBar: React.FC<{ shouldFail?: boolean }> = ({
  shouldFail = false,
}) => {
  if (shouldFail) {
    throw new Error("Failed to load summary data");
  }
  return (
    <div className="w-full rounded-md shadow border border-gray-200">
      <div className="flex divide-x divide-gray-200">
        {["Sites", "Fleets", "Devices", "Monitors"].map((title, index) => (
          <div key={title} className="flex-1 p-3 text-center">
            <div className="text-2xl font-light text-gray-800">{index + 5}</div>
            <div className="text-xs text-gray-600">{title}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const ScopedErrorDemo: Story = {
  render: () => {
    const [failedComponents, setFailedComponents] = React.useState<string[]>(
      [],
    );

    const toggleFailure = (component: string) => {
      setFailedComponents((prev) =>
        prev.includes(component)
          ? prev.filter((c) => c !== component)
          : [...prev, component],
      );
    };

    return (
      <div className="space-y-6 p-4">
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">Scoped Error Boundary Demo</h3>
          <p className="text-sm text-gray-600">
            Toggle errors for individual components to see how error boundaries
            contain failures
          </p>
          <div className="flex gap-2">
            {["summary", "place", "device"].map((component) => (
              <button
                key={component}
                type="button"
                onClick={() => toggleFailure(component)}
                className={`px-3 py-1 text-xs rounded ${
                  failedComponents.includes(component)
                    ? "bg-red-100 text-red-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                {failedComponents.includes(component) ? "Fix" : "Break"}{" "}
                {component}
              </button>
            ))}
          </div>
        </div>

        {/* Summary Bar */}
        <div>
          <h4 className="text-sm font-medium mb-2">Summary Bar (Dashboard)</h4>
          <ErrorBoundary>
            <MockSummaryBar shouldFail={failedComponents.includes("summary")} />
          </ErrorBoundary>
        </div>

        {/* Place Tiles Grid */}
        <div>
          <h4 className="text-sm font-medium mb-2">
            Place Tiles (Sites/Fleets Listing)
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ErrorBoundary>
              <MockPlaceTile shouldFail={failedComponents.includes("place")} />
            </ErrorBoundary>
            <ErrorBoundary>
              <MockPlaceTile />
            </ErrorBoundary>
          </div>
        </div>

        {/* Device Tiles Grid */}
        <div>
          <h4 className="text-sm font-medium mb-2">
            Device Tiles (Devices Page)
          </h4>
          <div className="flex gap-4 flex-wrap">
            <ErrorBoundary>
              <MockDeviceTile
                shouldFail={failedComponents.includes("device")}
              />
            </ErrorBoundary>
            <ErrorBoundary>
              <MockDeviceTile />
            </ErrorBoundary>
            <ErrorBoundary>
              <MockDeviceTile />
            </ErrorBoundary>
          </div>
        </div>
      </div>
    );
  },
};
