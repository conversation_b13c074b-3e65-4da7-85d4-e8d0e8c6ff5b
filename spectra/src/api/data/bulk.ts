import { formatDate } from "api/data";
import { useAuthFetch } from "context/AuthContext";
import type { Dayjs } from "utils/dayjs";
import { BASE_URL } from "./common";

export type Datapoint = {
  type: string;
  value: number | string;
};

export const useBulkDataApi = () => {
  const { authFetch } = useAuthFetch();

  const getThingLastEventTimesForPlace = async (
    placeType: string,
    placeId: string,
    simulationId: string | null,
    lookbackInDays = 30,
  ): Promise<Record<string, string>> => {
    const urlParams = new URLSearchParams();
    urlParams.set("lookbackInDays", lookbackInDays.toString());

    if (simulationId) {
      urlParams.set("simulationId", simulationId);
    }

    const url = `${BASE_URL}/${placeType}/${placeId}/things/latestEventTime?${urlParams.toString()}`;

    try {
      const response = await authFetch(url, {
        method: "GET",
        headers: {
          Accept: "*/*",
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error, status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error("Error getting thing last event time:", error);
      throw error;
    }
  };

  const getAllThingLastEventTimes = async (
    lookbackInDays = 30,
  ): Promise<Record<string, string>> => {
    const urlParams = new URLSearchParams();
    urlParams.set("lookbackInDays", lookbackInDays.toString());

    const url = `${BASE_URL}/things/latestEventTime?${urlParams.toString()}`;

    try {
      const response = await authFetch(url, {
        method: "GET",
        headers: {
          Accept: "*/*",
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error, status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error("Error getting all thing last event times:", error);
      throw error;
    }
  };

  const getThingSummariesForPlace = async (
    placeType: string,
    placeId: string,
    start: Dayjs,
    end: Dayjs,
    simulationId: string | null,
  ): Promise<Record<string, Datapoint[]>> => {
    const urlParams = new URLSearchParams();
    urlParams.set("startTime", formatDate(start));
    urlParams.set("endTime", formatDate(end));

    if (simulationId) {
      urlParams.set("simulationId", simulationId);
    }

    const url = `${BASE_URL}/${placeType}/${placeId}/things/summary?${urlParams.toString()}`;

    try {
      const response = await authFetch(url, {
        method: "GET",
        headers: {
          Accept: "*/*",
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error, status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error("Error getting thing summaries for place:", error);
      throw error;
    }
  };

  const getAllThingSummaries = async (
    start: Dayjs,
    end: Dayjs,
  ): Promise<Record<string, Datapoint[]>> => {
    const urlParams = new URLSearchParams();
    urlParams.set("startTime", formatDate(start));
    urlParams.set("endTime", formatDate(end));

    const url = `${BASE_URL}/things/summary?${urlParams.toString()}`;

    try {
      const response = await authFetch(url, {
        method: "GET",
        headers: {
          Accept: "*/*",
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error, status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error("Error getting all thing summaries:", error);
      throw error;
    }
  };

  return {
    getThingLastEventTimesForPlace, // TODO: validate this call
    getAllThingLastEventTimes,
    getThingSummariesForPlace, // TODO: validate this call
    getAllThingSummaries,
  };
};
