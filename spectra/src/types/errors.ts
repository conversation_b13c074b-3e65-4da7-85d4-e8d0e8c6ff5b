/**
 * Error types and interfaces for centralized error management
 */

export type ErrorSeverity = "low" | "medium" | "high" | "critical";

export type ErrorCategory =
  | "network"
  | "authentication"
  | "authorization"
  | "validation"
  | "server"
  | "client"
  | "runtime"
  | "unknown";

export type ErrorAction = {
  label: string;
  action: () => void;
  variant?: "primary" | "secondary";
};

export interface AppError {
  id: string;
  title: string;
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  timestamp: Date;
  details?: Record<string, unknown>;
  actions?: ErrorAction[];
  dismissible?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number; // in milliseconds
  retryable?: boolean;
  onRetry?: () => void;
  type: string;
  stack?: string;
  componentStack?: string;
}

export interface NetworkError extends Omit<AppError, "category"> {
  category: "network";
  statusCode?: number;
  url?: string;
  method?: string;
}

export interface AuthenticationError extends Omit<AppError, "category"> {
  category: "authentication";
  redirectToLogin?: boolean;
}

export interface ValidationError extends Omit<AppError, "category"> {
  category: "validation";
  field?: string;
  fieldErrors?: Record<string, string[]>;
}

export interface ServerError extends Omit<AppError, "category"> {
  category: "server";
  statusCode: number;
  errorCode?: string;
}

export interface RuntimeError extends Omit<AppError, "category"> {
  category: "runtime";
  stack?: string;
  componentStack?: string;
}

export type AnyError =
  | AppError
  | NetworkError
  | AuthenticationError
  | ValidationError
  | ServerError
  | RuntimeError;

/**
 * Error configuration for different error types
 */
export const ERROR_CONFIGS: Record<
  ErrorCategory,
  {
    defaultSeverity: ErrorSeverity;
    defaultAutoHide: boolean;
    defaultAutoHideDelay: number;
    defaultDismissible: boolean;
  }
> = {
  network: {
    defaultSeverity: "medium",
    defaultAutoHide: true,
    defaultAutoHideDelay: 5000,
    defaultDismissible: true,
  },
  authentication: {
    defaultSeverity: "high",
    defaultAutoHide: false,
    defaultAutoHideDelay: 0,
    defaultDismissible: false,
  },
  authorization: {
    defaultSeverity: "high",
    defaultAutoHide: false,
    defaultAutoHideDelay: 0,
    defaultDismissible: true,
  },
  validation: {
    defaultSeverity: "medium",
    defaultAutoHide: false,
    defaultAutoHideDelay: 0,
    defaultDismissible: true,
  },
  server: {
    defaultSeverity: "high",
    defaultAutoHide: true,
    defaultAutoHideDelay: 8000,
    defaultDismissible: true,
  },
  client: {
    defaultSeverity: "medium",
    defaultAutoHide: true,
    defaultAutoHideDelay: 5000,
    defaultDismissible: true,
  },
  runtime: {
    defaultSeverity: "critical",
    defaultAutoHide: false,
    defaultAutoHideDelay: 0,
    defaultDismissible: true,
  },
  unknown: {
    defaultSeverity: "medium",
    defaultAutoHide: true,
    defaultAutoHideDelay: 5000,
    defaultDismissible: true,
  },
};

/**
 * HTTP status code to error category mapping
 */
export const HTTP_STATUS_TO_CATEGORY: Record<number, ErrorCategory> = {
  400: "validation",
  401: "authentication",
  403: "authorization",
  404: "client",
  408: "network",
  429: "server",
  500: "server",
  502: "server",
  503: "server",
  504: "network",
};

/**
 * User-friendly error messages for common scenarios
 */
export const ERROR_MESSAGES = {
  NETWORK_OFFLINE:
    "You appear to be offline. Please check your internet connection.",
  NETWORK_TIMEOUT: "The request timed out. Please try again.",
  AUTHENTICATION_REQUIRED: "Please log in to continue.",
  AUTHENTICATION_EXPIRED: "Your session has expired. Please log in again.",
  AUTHORIZATION_DENIED: "You do not have permission to perform this action.",
  SERVER_ERROR: "Something went wrong on our end. Please try again later.",
  VALIDATION_FAILED: "Please check your input and try again.",
  UNKNOWN_ERROR: "An unexpected error occurred. Please try again.",
} as const;
