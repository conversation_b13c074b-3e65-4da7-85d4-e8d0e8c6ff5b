export interface ApiToken {
  tokenId: string;
  tokenName: string;
  tokenType: "INTERNAL" | "EXTERNAL";
  tokenCreatedAt: string;
  tokenUpdatedAt: string;

  tokenDeviceScope: string;
  tokenActionsScope: string;

  revenue30d: number | null;
  revenuePerUse: number | null;
  tokenUsage: number[]; // uses per hour
}

export const API_TOKEN_RESPONSE: ApiToken[] = [
  {
    tokenId: "fd5b5e6e-d06b-4414-8dec-846228ea2de0",
    tokenName: "Developer Token",
    tokenType: "INTERNAL",
    tokenCreatedAt: "2024-08-14T00:28:16.260450",
    tokenUpdatedAt: "2024-11-15T00:28:16.260450",
    tokenDeviceScope: "*",
    tokenActionsScope: "*",
    revenue30d: null,
    revenuePerUse: null,
    tokenUsage: [
      44, 10, 90, 44, 40, 50, 18, 54, 112, 58, 4, 50, 16, 68, 88, 34, 26, 32,
      86, 68, 18, 30, 114, 88,
    ],
  },
  {
    tokenId: "63252d73-e9be-4266-8b8a-4384d8ee600c",
    tokenName: "3rd Party Customer Token 2",
    tokenType: "EXTERNAL",
    tokenCreatedAt: "2025-02-26T00:28:16.260495",
    tokenUpdatedAt: "2025-05-15T12:28:16.260495",
    tokenDeviceScope: "customer/*",
    tokenActionsScope: "read/*, write/*",
    revenue30d: 3024,
    revenuePerUse: 0.05,
    tokenUsage: [
      177, 51, 147, 72, 9, 12, 111, 33, 177, 174, 99, 57, 108, 30, 90, 45, 63,
      54, 15, 60, 66, 102, 153, 111,
    ],
  },
  {
    tokenId: "bdcfd640-8bb7-449d-9ebf-5cb8819b0d98",
    tokenName: "Test Token",
    tokenType: "INTERNAL",
    tokenCreatedAt: "2025-05-21T00:28:16.260520",
    tokenUpdatedAt: "2025-11-19T00:28:16.260520",
    tokenDeviceScope: "battery/*",
    tokenActionsScope: "read/*",
    revenue30d: null,
    revenuePerUse: null,
    tokenUsage: [
      31, 60, 30, 24, 55, 48, 45, 16, 30, 7, 35, 35, 13, 30, 37, 20, 8, 1, 6,
      13, 22, 58, 39, 34,
    ],
  },
  {
    tokenId: "2d5480b8-3a55-45b8-a3e6-a18ad1194144",
    tokenName: "3rd Party Customer Token 1",
    tokenType: "EXTERNAL",
    tokenCreatedAt: "2024-09-15T00:28:16.260538",
    tokenUpdatedAt: "2024-10-21T12:28:16.260538",
    tokenDeviceScope: "customer/*",
    tokenActionsScope: "read/*",
    revenue30d: 3267,
    revenuePerUse: 0.05,
    tokenUsage: [
      135, 84, 144, 18, 144, 42, 48, 84, 81, 51, 51, 63, 102, 162, 180, 123, 45,
      12, 66, 120, 18, 96, 171, 138,
    ],
  },
  {
    tokenId: "bb080af2-8db1-4679-a651-7de433efa94f",
    tokenName: "Analytics Export",
    tokenType: "INTERNAL",
    tokenCreatedAt: "2024-09-15T00:28:16.260556",
    tokenUpdatedAt: "2024-12-26T12:28:16.260556",
    tokenDeviceScope: "*",
    tokenActionsScope: "read/*",
    revenue30d: null,
    revenuePerUse: null,
    tokenUsage: [
      600, 380, 270, 240, 480, 460, 180, 130, 190, 550, 100, 180, 170, 180, 270,
      60, 560, 160, 450, 250, 520, 510, 450, 160,
    ],
  },
  {
    tokenId: "e3424ab4-0ad4-4994-b9b6-5d68a05ffe04",
    tokenName: "3rd Party Customer Development Token",
    tokenType: "EXTERNAL",
    tokenCreatedAt: "2024-12-15T00:28:16.260572",
    tokenUpdatedAt: "2025-04-15T12:28:16.260572",
    tokenDeviceScope: "customer/*",
    tokenActionsScope: "read/*, write/*",
    revenue30d: 10530,
    revenuePerUse: 0.05,
    tokenUsage: [
      130, 280, 240, 170, 470, 20, 330, 280, 300, 380, 440, 470, 270, 70, 500,
      530, 60, 170, 320, 600, 10, 310, 440, 230,
    ],
  },
  {
    tokenId: "f5f4b684-229a-4ab3-b4bf-995b7d089417",
    tokenName: "Test",
    tokenType: "INTERNAL",
    tokenCreatedAt: "2024-11-04T00:28:16.260589",
    tokenUpdatedAt: "2025-03-17T00:28:16.260589",
    tokenDeviceScope: "*",
    tokenActionsScope: "*",
    revenue30d: null,
    revenuePerUse: null,
    tokenUsage: [
      5, 44, 21, 9, 38, 50, 17, 6, 43, 13, 21, 39, 27, 39, 45, 9, 36, 24, 47,
      58, 29, 36, 33, 9,
    ],
  },
];
