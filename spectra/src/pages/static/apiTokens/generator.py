import datetime
import json
import random
import uuid

NUM_TOKENS = 7

tokens = []


for i in range(NUM_TOKENS):
    # create a random date between 1 year ago and now
    token_created_at = datetime.datetime.now() - datetime.timedelta(days=random.randint(0, 365))
    token_updated_at = token_created_at + datetime.timedelta(days=random.randint(0, 365)) / 2

    # create a usage multiplier between 1 and 10
    usage_multiplier = random.randint(1, 10)
    revenuePerUse = None if i % 2 == 0 else 0.05

    # create 24h usage
    usage = [usage_multiplier * random.randint(0, 60) for _ in range(24)]

    # create a revenue based on the usage and revenuePerUse``
    revenue30d = sum(usage) * revenuePerUse * 30 if revenuePerUse else 0

    tokens.append({
        "tokenId": str(uuid.uuid4()),
        "tokenName": f"Token {i+1}",
        "tokenType": "INTERNAL" if i % 2 == 0 else "EXTERNAL",
        "tokenCreatedAt": token_created_at.isoformat(),
        "tokenUpdatedAt": token_updated_at.isoformat(),
        "tokenDeviceScope": "*",
        "tokenActionsScope": "*",
        "revenue30d": revenue30d,
        "revenuePerUse": revenuePerUse,
        "tokenUsage": usage,
    })

# pretty print the tokens
print(json.dumps(tokens, indent=4))
