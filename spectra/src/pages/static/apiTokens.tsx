// this is a static page for demo, please try not to use it as a reference for anything

import SelectField from "components/uikit/selectField";
import TextField from "components/uikit/textField";
import { useEffect, useState } from "react";
import { API_TOKEN_RESPONSE, type ApiToken } from "./apiTokens/tokens";

import { ReactComponent as DeleteIcon } from "images/icons/delete.svg";
import { ReactComponent as Chart } from "./apiTokens/chart.svg";

const Header = () => {
  return (
    <div className="flex pt-2 pb-4 border-b border-space90 justify-between">
      <p className="text-heading1 text-space50">API Tokens</p>
      <div className="flex gap-2">
        {/* <button
          type="button"
          className="px-3.5 py-2 rounded-full border border-space80 justify-end items-center gap-1 cursor-pointer hover:bg-gray95 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue90 focus:ring-blue90 flex"
        >
          <div className="text-space70 text-xs font-medium leading-[14px]">
            + Create Token
          </div>
        </button> */}
      </div>
    </div>
  );
};

const SummaryCard = ({
  title,
  value,
  ready,
}: { title: string; value: string; ready: boolean }) => {
  return (
    <div className="flex p-4 bg-white rounded-lg outline outline-1 outline-offset-[-1px] outline-gray95 flex-row justify-between flex-1 gap-4">
      <div className="flex flex-col justify-start items-start">
        <div className="text-space50 text-2xl">{value}</div>
        <div className="text-space70 text-xs">{title}</div>
      </div>
      <div className="flex flex-1 flex-col">
        {ready ? (
          <Chart className="w-full" />
        ) : (
          <div className="w-full h-full bg-gray95 animate-pulse" />
        )}
      </div>
    </div>
  );
};

const Summary = ({ tokens }: { tokens: ApiToken[] | null }) => {
  const totalTokens = tokens?.length;
  const totalUsage = tokens?.reduce(
    (acc, token) =>
      acc + token.tokenUsage.reduce((acc, usage) => acc + usage, 0),
    0,
  );
  const totalRevenue = tokens?.reduce(
    (acc, token) => acc + token.revenue30d,
    0,
  );
  const ready = tokens !== null && tokens.length > 0;

  return (
    <div className="flex flex-row gap-4">
      <SummaryCard
        title="Tokens"
        value={totalTokens?.toLocaleString() ?? "Loading..."}
        ready={ready}
      />
      <SummaryCard
        title="24h usage"
        value={totalUsage?.toLocaleString() ?? "Loading..."}
        ready={ready}
      />
      <SummaryCard
        title="Last 30d revenue"
        value={
          totalRevenue?.toLocaleString("en-US", {
            style: "currency",
            currency: "USD",
            notation: "compact",
          }) ?? "Loading..."
        }
        ready={ready}
      />
    </div>
  );
};

const Filters = ({
  tokens,
  onFilterChange,
}: {
  tokens: ApiToken[];
  onFilterChange: (tokens: ApiToken[]) => void;
}) => {
  const [nameFilter, setNameFilter] = useState("");
  const [typeFilter, setTypeFilter] = useState("");

  useEffect(() => {
    const filteredTokens = tokens.filter((token) => {
      const matchesName = token.tokenName
        .toLowerCase()
        .includes(nameFilter.toLowerCase());
      const matchesType = !typeFilter || token.tokenType === typeFilter;
      return matchesName && matchesType;
    });
    onFilterChange(filteredTokens);
  }, [tokens, nameFilter, typeFilter, onFilterChange]);

  const typeOptions = [
    { value: "", label: "All Types" },
    { value: "EXTERNAL", label: "External" },
    { value: "INTERNAL", label: "Internal" },
  ];

  return (
    <div className="flex flex-col gap-4 mb-4">
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 min-w-[200px]">
          <TextField
            label="Name"
            value={nameFilter}
            onChange={setNameFilter}
            placeholder="Filter by name..."
          />
        </div>

        <div className="w-[200px]">
          <SelectField
            label="Type"
            value={typeFilter}
            options={typeOptions}
            onChange={setTypeFilter}
            placeholder="Select type"
          />
        </div>
      </div>
    </div>
  );
};

const TokenRow = ({ token }: { token: ApiToken }) => {
  const tokenUsageMax = Math.max(...token.tokenUsage);
  return (
    <div className="w-full flex flex-col items-start pb-4 group cursor-pointer">
      <div className="w-full flex items-center gap-16">
        <div className="flex-1 inline-flex flex-col items-start gap-1">
          <div className="flex flex-col items-start">
            <div className="inline-flex justify-center items-center gap-2.5">
              <div className="text-space50 text-base font-medium">
                {token.tokenName}
              </div>
              {token.tokenType === "EXTERNAL" && (
                <div className="px-2 py-1 bg-blue95 rounded flex justify-center items-center gap-2.5">
                  <div className=" text-blue50 text-xs font-bold uppercase">
                    External
                  </div>
                </div>
              )}
            </div>
            <div className="text-space70 text-xs font-mono">
              {token.tokenId}
            </div>
            <div className="text-space70 text-xs font-mono">
              Created {new Date(token.tokenCreatedAt).toLocaleString()}
            </div>
          </div>
        </div>
        <div className="inline-flex flex-col justify-center items-start">
          <div className="self-stretch text-black text-2xl ">
            {token.tokenUsage
              .reduce((acc, usage) => acc + usage, 0)
              .toLocaleString()}
          </div>
          <div className="self-stretch text-gray70 text-xs ">
            last 24h usage
          </div>
        </div>
        <div className="inline-flex flex-col justify-center items-start">
          <div className="self-stretch text-black text-2xl">
            {token.revenue30d
              ? token.revenue30d.toLocaleString("en-US", {
                  style: "currency",
                  currency: "USD",
                  notation: "compact",
                })
              : "-"}
          </div>
          <div className="self-stretch  text-gray70 text-xs ">
            last 30d revenue
          </div>
        </div>
        <div className="flex-1 inline-flex flex-col  items-start gap-1">
          <div className=" text-space50 text-xs font-mono">
            Device Scope: {token.tokenDeviceScope}
          </div>
          <div className=" text-space50 text-xs font-mono">
            Actions Scope: {token.tokenActionsScope}
          </div>
        </div>
        <div className="self-stretch flex items-center gap-3">
          <div className="self-stretch rounded flex justify-center items-center gap-2.5">
            <div className="text-gray70 cursor-not-allowed">
              <DeleteIcon />
            </div>
          </div>
        </div>
      </div>
      <div className="w-full flex flex-col pt-2 items-start gap-0.5">
        <div className="w-full h-20 inline-flex items-center gap-px">
          {token.tokenUsage.map((usage) => (
            <div
              key={usage}
              className="flex-1 self-stretch inline-flex flex-col items-start"
            >
              <div className="self-stretch flex-1 group-hover:bg-blue95 bg-space95 transition-colors duration-200" />
              <div
                className="self-stretch group-hover:bg-blue40 bg-space70 transition-colors duration-200"
                style={{ height: `${(usage / tokenUsageMax) * 100}%` }}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const Tokens = ({ tokens }: { tokens: ApiToken[] }) => {
  return (
    <div className="flex flex-col gap-4">
      {tokens.length === 0 && (
        <div className="text-space70 text-body text-center">
          No tokens found for selected filters
        </div>
      )}
      {tokens.map((token) => (
        <TokenRow key={token.tokenId} token={token} />
      ))}
    </div>
  );
};

const ApiTokensPage = () => {
  const [tokens, setTokens] = useState<ApiToken[] | null>(null);
  const [filteredTokens, setFilteredTokens] = useState<ApiToken[]>([]);

  useEffect(() => {
    const fetchTokens = async () => {
      setTimeout(() => {
        setTokens(API_TOKEN_RESPONSE);
      }, 1000);
    };
    fetchTokens();
  }, []);

  return (
    <div className="p-4 flex flex-col gap-4">
      <Header />

      <Summary tokens={tokens} />
      {tokens && <Filters tokens={tokens} onFilterChange={setFilteredTokens} />}
      {tokens ? <Tokens tokens={filteredTokens} /> : <p>Loading...</p>}
    </div>
  );
};

export { ApiTokensPage };
