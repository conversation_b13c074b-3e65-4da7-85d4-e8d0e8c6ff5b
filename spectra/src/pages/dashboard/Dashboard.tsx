import { useMemo } from "react";

import { DeviceDetail } from "components";
import DeviceStateDonutChart from "components/charts/DeviceStateDonutChart";
import { withScrollIndicators } from "components/uikit/ScrollIndicatorWrapper";
import { useSelectedDevice } from "context/SelectedDeviceContext";
import {
  SelectedTimeRangeProvider,
  TimeRangeOptions,
  TimeRangeSelector,
  useSelectedTimeRange,
} from "context/SelectedTimeRangeContext";
import { DeviceStates, useDeviceState } from "hooks/useDeviceState";
import { useNavigate } from "react-router-dom";
import {
  AssetMap,
  DeviceList,
  MonitorOverview,
  SummaryBar,
} from "./components";

// Color mapping for device states
const deviceStateColors = {
  [DeviceStates.ONLINE]: "#6E9F66", // green
  [DeviceStates.OFFLINE]: "#C55B57", // red
  [DeviceStates.UNKNOWN]: "#E87D48", // orange
  [DeviceStates.UNINITIALIZED]: "#D4D5D6", // gray
};

const DeviceStateSummary = ({
  stateCounts,
}: {
  stateCounts: Record<DeviceStates, number>;
}) => {
  return (
    <div className="space-y-3">
      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center space-x-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: deviceStateColors[DeviceStates.ONLINE] }}
          />
          <div className="text-sm">
            <span className="font-medium">
              {stateCounts[DeviceStates.ONLINE]}
            </span>
            <span className="text-gray-600 ml-1">Online</span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: deviceStateColors[DeviceStates.OFFLINE] }}
          />
          <div className="text-sm">
            <span className="font-medium">
              {stateCounts[DeviceStates.OFFLINE]}
            </span>
            <span className="text-gray-600 ml-1">Offline</span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: deviceStateColors[DeviceStates.UNKNOWN] }}
          />
          <div className="text-sm">
            <span className="font-medium">
              {stateCounts[DeviceStates.UNKNOWN]}
            </span>
            <span className="text-gray-600 ml-1">Deployed</span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{
              backgroundColor: deviceStateColors[DeviceStates.UNINITIALIZED],
            }}
          />
          <div className="text-sm">
            <span className="font-medium">
              {stateCounts[DeviceStates.UNINITIALIZED]}
            </span>
            <span className="text-gray-600 ml-1">Uninitialized</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Create a wrapped DeviceList with scroll indicators
const DeviceListWithScrollIndicators = withScrollIndicators(
  DeviceList,
  "w-full",
);

const PageContent = () => {
  const { start, end } = useSelectedTimeRange();
  const lookbackInDays = useMemo(() => end.diff(start, "days"), [start, end]);

  const { devices, isLoading } = useDeviceState(lookbackInDays);
  const navigate = useNavigate();

  // Calculate device state counts
  const stateCounts = useMemo(() => {
    const counts = {
      [DeviceStates.ONLINE]: 0,
      [DeviceStates.OFFLINE]: 0,
      [DeviceStates.UNKNOWN]: 0,
      [DeviceStates.UNINITIALIZED]: 0,
    };

    for (const device of devices) {
      if (device.deviceState && device.deviceState in counts) {
        counts[device.deviceState]++;
      }
    }

    return counts;
  }, [devices]);

  const map = (
    <div className="w-full h-full min-h-[350px] rounded-md shadow border border-gray95 flex flex-col">
      <AssetMap />
    </div>
  );

  const deviceStats = (
    <div
      className="w-full min-h-full rounded-md shadow border border-gray95 py-3 px-4 cursor-pointer"
      onClick={() => {
        navigate("/devices");
      }}
      onKeyDown={(e) => {
        if (e.key === "Enter") {
          navigate("/devices");
        }
      }}
    >
      <h2 className="text-lg font-semibold mb-4">Device Status</h2>
      <DeviceStateSummary stateCounts={stateCounts} />
      <div className="mt-4">
        <DeviceStateDonutChart
          stateCounts={stateCounts}
          colorMap={deviceStateColors}
          isLoading={isLoading}
          totalDevices={devices.length}
          innerRadius={60}
        />
      </div>
      <div className="text-xs text-gray-500 pt-2">
        Total devices: {devices.length}
      </div>
    </div>
  );

  const monitorStats = (
    <div
      className="w-full min-h-full rounded-md shadow border border-gray95 py-3 px-4 cursor-pointer"
      onClick={() => {
        navigate("/monitors");
      }}
      onKeyDown={(e) => {
        if (e.key === "Enter") {
          navigate("/monitors");
        }
      }}
    >
      <h2 className="text-lg font-semibold mb-4">Monitor Summary</h2>
      <MonitorOverview />
    </div>
  );

  return (
    <div className="flex flex-col min-h-screen w-full min-w-0 p-4">
      <div className="flex pb-5 justify-between">
        <p className="text-lg font-semibold">Asset Overview</p>
        <div className="flex gap-2">
          <TimeRangeSelector />
        </div>
      </div>

      <div className="w-full mb-4">
        <SummaryBar />
      </div>

      <div className="flex flex-col gap-2 @7xl:flex-row mb-6 xl:items-stretch">
        <div className="w-full @7xl:w-1/2">{map}</div>

        <div className="flex flex-col w-full @7xl:w-1/2 @3xl:flex-row gap-2 xl:items-stretch">
          <div className="w-full @7xl:w-1/2">{deviceStats}</div>
          <div className="w-full @7xl:w-1/2">{monitorStats}</div>
        </div>
      </div>

      {/* <SummaryStats stats={summaryStats} /> */}
      <h2 className="text-lg font-semibold mb-4">Devices</h2>
      <div className="w-full mb-4 rounded-md shadow border border-gray95 overflow-y-auto">
        <DeviceListWithScrollIndicators />
      </div>
    </div>
  );
};

const Dashboard = () => {
  const { selectedDevice, setSelectedDevice } = useSelectedDevice();
  const placeType = selectedDevice?.placeType?.toLowerCase() || "site";
  return (
    <SelectedTimeRangeProvider defaultTimeRange={TimeRangeOptions.Past7d}>
      <div className="flex flex-row gap-2 @container">
        <PageContent />
        {selectedDevice && (
          <DeviceDetail
            selectedDevice={selectedDevice}
            setSelectedDevice={setSelectedDevice}
            placeType={placeType}
          />
        )}
      </div>
    </SelectedTimeRangeProvider>
  );
};

export default Dashboard;
