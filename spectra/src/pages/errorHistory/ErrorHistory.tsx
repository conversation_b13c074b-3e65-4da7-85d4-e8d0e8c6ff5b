import { useCallback, useEffect, useRef, useState } from "react";
import ButtonComponent from "../../components/uikit/button";
import SelectField from "../../components/uikit/selectField";
import TextField from "../../components/uikit/textField";
import {
  type ErrorHistoryEntry,
  errorHistoryService,
} from "../../services/errorHistoryService";
import { useErrorActions, useSilentMode } from "../../stores/errorStore";

interface ErrorHistoryItemProps {
  entry: ErrorHistoryEntry;
  isExpanded: boolean;
  onToggleExpand: () => void;
  onDelete: (id: string) => void;
}

const ErrorHistoryItem: React.FC<ErrorHistoryItemProps> = ({
  entry,
  isExpanded,
  onToggleExpand,
  onDelete,
}) => {
  const [isEndpointExpanded, setIsEndpointExpanded] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const formatTimestamp = (date: Date) => {
    return new Date(date)
      .toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
      .replace(",", "");
  };

  const truncateText = (text: string, maxLength = 60) => {
    if (text.length <= maxLength) return text;
    return `${text.substring(0, maxLength)}...`;
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  const getFullEndpoint = () => {
    return `${entry.httpMethod ? `${entry.httpMethod} ` : ""}${entry.endpoint || ""}`;
  };

  const shouldTruncateEndpoint = (endpoint: string) => {
    return endpoint.length > 50;
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical":
        return "text-red-600 bg-red-50";
      case "high":
        return "text-orange-600 bg-orange-50";
      case "medium":
        return "text-yellow-600 bg-yellow-50";
      case "low":
        return "text-blue-600 bg-blue-50";
      default:
        return "text-gray-600 bg-gray-50";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "network":
        return "text-purple-600 bg-purple-50";
      case "validation":
        return "text-yellow-600 bg-yellow-50";
      case "auth":
        return "text-red-600 bg-red-50";
      case "server":
        return "text-orange-600 bg-orange-50";
      default:
        return "text-gray-600 bg-gray-50";
    }
  };

  const shouldShowExpand = entry.message.length > 60 || entry.stack;

  return (
    <div className="relative grid grid-cols-12 gap-4 py-3 px-4 text-sm hover:bg-gray-50 border-b border-gray-100">
      {shouldShowExpand && (
        <button
          type="button"
          onClick={onToggleExpand}
          className="absolute top-3 left-0 text-blue-900 text-xs flex items-center z-10"
          aria-label={isExpanded ? "Collapse details" : "Expand details"}
        >
          {isExpanded ? (
            <span className="text-sm text-blue-900">▲</span>
          ) : (
            <span className="text-sm text-blue-900">▼</span>
          )}
        </button>
      )}

      <div className="col-span-2 font-mono text-xs">
        {formatTimestamp(entry.timestamp)}
      </div>

      <div className="col-span-1">
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(entry.type)}`}
        >
          {entry.type}
        </span>
      </div>

      <div className="col-span-1">
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(entry.severity)}`}
        >
          {entry.severity}
        </span>
      </div>

      <div className="col-span-6">
        <div
          className={`${isExpanded ? "" : "truncate whitespace-nowrap overflow-hidden"}`}
        >
          <div className="font-medium text-gray-900">
            {isExpanded ? entry.message : truncateText(entry.message)}
          </div>
          {entry.source && (
            <div className="text-xs text-gray-500 mt-1">
              Source: {entry.source}
            </div>
          )}
          {entry.endpoint && (
            <div className="text-xs text-gray-600 mt-1">
              {/* Header row with label, expand button, copy button, and status */}
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium">API Endpoint:</span>

                {/* Expand/collapse button */}
                {shouldTruncateEndpoint(getFullEndpoint()) && (
                  <button
                    type="button"
                    onClick={() => setIsEndpointExpanded(!isEndpointExpanded)}
                    className="text-blue-900 hover:text-blue-700 flex-shrink-0"
                    aria-label={
                      isEndpointExpanded
                        ? "Collapse endpoint"
                        : "Expand endpoint"
                    }
                  >
                    <span className="text-xs transition-transform duration-200">
                      {isEndpointExpanded ? "▲" : "▼"}
                    </span>
                  </button>
                )}

                {/* Copy button */}
                <button
                  type="button"
                  onClick={() => copyToClipboard(getFullEndpoint())}
                  className="text-gray-500 hover:text-gray-700 flex-shrink-0 p-1 rounded hover:bg-gray-200 transition-colors"
                  title="Copy endpoint to clipboard"
                >
                  <svg
                    className="w-3 h-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <title>Copy to clipboard</title>
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                </button>

                {/* Copy success message */}
                {copySuccess && (
                  <span className="text-green-600 text-xs font-medium animate-pulse">
                    Copied!
                  </span>
                )}
              </div>

              {/* Endpoint content - expands vertically */}
              <div className="w-full flex items-center gap-2">
                <div
                  className={`font-mono bg-gray-100 px-2 py-1 rounded inline-block ${
                    !isEndpointExpanded &&
                    shouldTruncateEndpoint(getFullEndpoint())
                      ? "truncate max-w-full"
                      : "whitespace-pre-wrap break-words"
                  }`}
                  title={!isEndpointExpanded ? getFullEndpoint() : undefined}
                >
                  {entry.httpMethod && `${entry.httpMethod} `}
                  {entry.endpoint}
                </div>
                {/* HTTP Status next to endpoint */}
                {entry.httpStatus && (
                  <span
                    className={`px-2 py-0.5 rounded text-xs font-medium flex-shrink-0 ${
                      entry.httpStatus >= 500
                        ? "bg-red-100 text-red-700"
                        : entry.httpStatus >= 400
                          ? "bg-orange-100 text-orange-700"
                          : "bg-green-100 text-green-700"
                    }`}
                  >
                    {entry.httpStatus}
                  </span>
                )}
              </div>
            </div>
          )}
          {isExpanded && entry.stack && (
            <div className="mt-2 p-2 bg-gray-100 rounded text-xs font-mono text-gray-700 whitespace-pre-wrap">
              {entry.stack}
            </div>
          )}
          {isExpanded &&
            entry.metadata &&
            Object.keys(entry.metadata).length > 0 && (
              <div className="mt-2 p-2 bg-blue-50 rounded text-xs">
                <div className="font-medium text-blue-800 mb-1">
                  Additional Context:
                </div>
                <pre className="text-blue-700 whitespace-pre-wrap">
                  {JSON.stringify(entry.metadata, null, 2)}
                </pre>
              </div>
            )}
          {isExpanded && entry.url && (
            <div className="text-xs text-gray-500 mt-1">
              Page URL: <span className="font-mono break-all">{entry.url}</span>
            </div>
          )}
        </div>
      </div>

      <div className="col-span-2 text-right">
        <button
          type="button"
          onClick={() => onDelete(entry.id)}
          className="text-red-600 hover:text-red-800 text-xs font-medium"
          title="Delete error"
        >
          Delete
        </button>
      </div>
    </div>
  );
};

interface ErrorHistoryProps {
  className?: string;
}

export const ErrorHistory: React.FC<ErrorHistoryProps> = ({
  className = "",
}) => {
  const [displayedErrors, setDisplayedErrors] = useState<ErrorHistoryEntry[]>(
    [],
  );
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [filterText, setFilterText] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("");
  const [severityFilter, setSeverityFilter] = useState<string>("");
  const [stats, setStats] = useState<{
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    last24Hours: number;
  } | null>(null);

  const { setSilentMode, markErrorHistoryVisited } = useErrorActions();
  const silentMode = useSilentMode();
  const [currentOffset, setCurrentOffset] = useState(0);
  const currentOffsetRef = useRef(0);

  const loadErrors = useCallback(
    async (reset = false) => {
      setIsLoading(true);
      try {
        const offset = reset ? 0 : currentOffsetRef.current;
        const errors = await errorHistoryService.getErrors({
          limit: 10,
          offset,
          type: typeFilter || undefined,
          severity: severityFilter || undefined,
        });

        if (reset) {
          setDisplayedErrors(errors);
          currentOffsetRef.current = errors.length;
          setCurrentOffset(errors.length);
        } else {
          setDisplayedErrors((prev) => [...prev, ...errors]);
          currentOffsetRef.current += errors.length;
          setCurrentOffset((prev) => prev + errors.length);
        }

        setHasMore(errors.length === 10);
      } catch (error) {
        console.error("Failed to load error history:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [typeFilter, severityFilter],
  );

  const loadStats = useCallback(async () => {
    try {
      const errorStats = await errorHistoryService.getErrorStats();
      setStats(errorStats);
    } catch (error) {
      console.error("Failed to load error statistics:", error);
    }
  }, []);

  const toggleExpand = (id: string) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleClearAll = async () => {
    if (
      window.confirm(
        "Are you sure you want to clear all error history? This action cannot be undone.",
      )
    ) {
      try {
        await errorHistoryService.clearAllErrors();
        setDisplayedErrors([]);
        setCurrentOffset(0);
        setHasMore(false);
        await loadStats();
      } catch (error) {
        console.error("Failed to clear error history:", error);
      }
    }
  };

  const handleDelete = async (id: string) => {
    if (
      window.confirm(
        "Are you sure you want to delete this error? This action cannot be undone.",
      )
    ) {
      try {
        await errorHistoryService.deleteError(id);
        setDisplayedErrors((prev) => prev.filter((error) => error.id !== id));
        await loadStats();
      } catch (error) {
        console.error("Failed to delete error:", error);
      }
    }
  };

  const filteredErrors = displayedErrors.filter(
    (entry) =>
      filterText === "" ||
      entry.message.toLowerCase().includes(filterText.toLowerCase()) ||
      entry.type.toLowerCase().includes(filterText.toLowerCase()) ||
      entry.source?.toLowerCase().includes(filterText.toLowerCase()) ||
      entry.endpoint?.toLowerCase().includes(filterText.toLowerCase()) ||
      entry.httpMethod?.toLowerCase().includes(filterText.toLowerCase()),
  );

  useEffect(() => {
    loadErrors(true);
    loadStats();
    markErrorHistoryVisited();
  }, [loadErrors, loadStats, markErrorHistoryVisited]);

  useEffect(() => {
    // Set up periodic refresh for stats
    const interval = setInterval(loadStats, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [loadStats]);

  return (
    <div className={`bg-white ${className}`}>
      {/* Stats Summary */}
      {stats && (
        <div className="p-4 bg-gray-50 border-b">
          <div className="grid grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {stats.total}
              </div>
              <div className="text-sm text-gray-600">Total Errors</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-orange-600">
                {stats.last24Hours}
              </div>
              <div className="text-sm text-gray-600">Last 24 Hours</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {Object.entries(stats.byType).sort(
                  ([, a], [, b]) => b - a,
                )[0]?.[0] || "None"}
              </div>
              <div className="text-sm text-gray-600">Most Common Type</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">
                {stats.bySeverity.critical || 0}
              </div>
              <div className="text-sm text-gray-600">Critical Errors</div>
            </div>
          </div>
        </div>
      )}

      {/* Header with filters */}
      <div className="flex items-end gap-4 p-4 border-b">
        <div className="flex-1">
          <TextField
            label="Search"
            value={filterText}
            onChange={setFilterText}
            placeholder="Filter errors..."
            className="w-full"
          />
        </div>

        <div className="flex-shrink-0">
          <SelectField
            label="Type"
            value={typeFilter}
            onChange={setTypeFilter}
            placeholder="All Types"
            options={[
              { value: "", label: "All Types" },
              { value: "network", label: "Network" },
              { value: "validation", label: "Validation" },
              { value: "auth", label: "Authentication" },
              { value: "server", label: "Server" },
              { value: "unknown", label: "Unknown" },
            ]}
            className="w-48"
          />
        </div>

        <div className="flex-shrink-0">
          <SelectField
            label="Severity"
            value={severityFilter}
            onChange={setSeverityFilter}
            placeholder="All Severities"
            options={[
              { value: "", label: "All Severities" },
              { value: "low", label: "Low" },
              { value: "medium", label: "Medium" },
              { value: "high", label: "High" },
              { value: "critical", label: "Critical" },
            ]}
            className="w-48"
          />
        </div>

        <ButtonComponent.Default
          variant="filled"
          buttonStyle="danger"
          onClick={handleClearAll}
          className="h-[60px]"
        >
          Clear All
        </ButtonComponent.Default>
      </div>

      {/* Silent Mode */}
      <div className="px-4 py-3 border-b bg-gray-50">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="silentMode"
            checked={silentMode}
            onChange={(e) => setSilentMode(e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="silentMode" className="ml-2 text-sm text-gray-600">
            Hide notifications
          </label>
        </div>
      </div>

      {/* Table header */}
      <div className="grid grid-cols-12 gap-4 py-3 px-4 text-xs font-medium text-gray-600 tracking-wider bg-gray-50 border-b">
        <div className="col-span-2">Timestamp</div>
        <div className="col-span-1">Type</div>
        <div className="col-span-1">Severity</div>
        <div className="col-span-6">Error Message</div>
        <div className="col-span-2 text-right">Actions</div>
      </div>

      {/* Error entries */}
      <div>
        {filteredErrors.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            {isLoading ? "Loading errors..." : "No errors found"}
          </div>
        ) : (
          filteredErrors.map((entry) => (
            <ErrorHistoryItem
              key={entry.id}
              entry={entry}
              isExpanded={expandedItems.has(entry.id)}
              onToggleExpand={() => toggleExpand(entry.id)}
              onDelete={handleDelete}
            />
          ))
        )}
      </div>

      {/* Load more button */}
      {hasMore && !isLoading && filteredErrors.length > 0 && (
        <div className="p-4 border-t">
          <button
            type="button"
            onClick={() => loadErrors(false)}
            className="py-2 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            Load more...
          </button>
        </div>
      )}

      {isLoading && <div className="p-4 text-gray-500 text-sm">Loading...</div>}
    </div>
  );
};
