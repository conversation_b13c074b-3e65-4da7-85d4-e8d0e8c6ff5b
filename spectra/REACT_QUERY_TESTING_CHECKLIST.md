# React Query Conversion Testing Checklist

## 🎯 Test Route Created

**URL**: `http://localhost:3001/react-query-demo`

**Access**: Available in sidebar footer for Aerovy team members (🚀 React Query Demo)

## ✅ Pre-Testing Setup

### 1. Start the Application
```bash
cd spectra
bun start
```

### 2. Navigate to Demo
- Open browser to `http://localhost:3001`
- Login with your credentials
- Look for "🚀 React Query Demo" in the sidebar footer
- Click to navigate to `/react-query-demo`

## 📋 Testing Checklist

### ✅ Basic Functionality Tests

#### 1. **Initial Load Test**
- [ ] Demo page loads without errors
- [ ] Device statistics display correctly (Total, Online, Offline, Unknown, Uninitialized)
- [ ] Recent devices list shows up to 5 devices
- [ ] Loading state appears briefly during initial load
- [ ] No console errors in browser DevTools

#### 2. **Data Display Test**
- [ ] Device names and IDs are displayed
- [ ] Device states show correct colors:
  - Green for ONLINE
  - Red for OFFLINE  
  - Yellow for UNKNOWN
  - Gray for UNINITIALIZED
- [ ] Last event times are formatted correctly
- [ ] Statistics numbers match the device list

#### 3. **Manual Refresh Test**
- [ ] Click "Refresh" button
- [ ] Loading state appears briefly
- [ ] Data refreshes (timestamps may update)
- [ ] No errors during refresh

#### 4. **Lookback Period Test**
- [ ] Change dropdown from "30 days" to "7 days"
- [ ] Data automatically refetches
- [ ] Statistics may change based on different lookback period
- [ ] Change to "90 days" and verify it works

### ✅ React Query Specific Tests

#### 5. **Caching Test**
- [ ] Navigate away from demo page (e.g., to Dashboard)
- [ ] Navigate back to demo page
- [ ] Data loads instantly (from cache)
- [ ] No loading spinner on return visit
- [ ] Background refresh may occur after 30 seconds

#### 6. **Background Updates Test**
- [ ] Stay on demo page for 60+ seconds
- [ ] Watch for automatic background refresh
- [ ] Data updates without showing loading spinner
- [ ] Timestamps may update to reflect fresh data

#### 7. **Multiple Component Test**
- [ ] Open browser DevTools → Network tab
- [ ] Clear network log
- [ ] Refresh the demo page
- [ ] Verify only ONE set of API calls (devices + event times)
- [ ] No duplicate requests should appear

#### 8. **Error Handling Test**
- [ ] Disconnect internet or block API calls
- [ ] Refresh the page
- [ ] Error state should display with retry button
- [ ] Click retry button
- [ ] Reconnect internet
- [ ] Data should load successfully

### ✅ Performance Tests

#### 9. **Network Performance**
- [ ] Open DevTools → Network tab
- [ ] Clear cache and hard refresh
- [ ] Observe API calls:
  - Should see parallel requests (devices + event times)
  - Total load time should be faster than sequential
- [ ] Check for request deduplication (no duplicate calls)

#### 10. **Memory Performance**
- [ ] Open DevTools → Performance tab
- [ ] Record performance while navigating to/from demo
- [ ] Check for memory leaks or excessive re-renders
- [ ] React Query should prevent unnecessary re-renders

### ✅ Integration Tests

#### 11. **Existing Components Test**
- [ ] Navigate to Dashboard page
- [ ] Check if any components use `useDeviceState`
- [ ] Verify they still work correctly
- [ ] No breaking changes should occur

#### 12. **Cross-Component Caching**
- [ ] If Dashboard uses device data, check for shared cache
- [ ] Data should load instantly if already cached
- [ ] Updates in one component should reflect in others

## 🔍 Browser DevTools Verification

### React Query DevTools (Optional)
If you want to see React Query in action:

1. Install React Query DevTools:
```bash
bun add @tanstack/react-query-devtools
```

2. Add to App.js:
```javascript
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

// Add inside QueryClientProvider
<ReactQueryDevtools initialIsOpen={false} />
```

3. Look for floating React Query icon in bottom corner
4. Click to see query cache, status, and timing

### Network Tab Verification
- [ ] API calls to `/things` and `/things/latestEventTime`
- [ ] Parallel execution (both start around same time)
- [ ] Proper caching (subsequent visits use cache)
- [ ] Background refetch after 60 seconds

### Console Verification
- [ ] No React warnings or errors
- [ ] No "useEffect dependency" warnings (should be eliminated)
- [ ] Clean console output

## 🚨 Common Issues to Watch For

### ❌ Potential Problems
1. **CORS Issues**: API calls fail due to authentication
2. **Import Errors**: Missing dependencies or wrong paths
3. **Type Errors**: TypeScript compilation issues
4. **Infinite Loops**: Incorrect query dependencies
5. **Memory Leaks**: Components not cleaning up properly

### ✅ Expected Behavior
1. **Fast Initial Load**: ~50% faster than before
2. **Instant Cache Hits**: Subsequent loads are immediate
3. **Background Updates**: Fresh data without loading spinners
4. **Error Recovery**: Automatic retry on failures
5. **No Duplicate Requests**: Shared cache prevents redundant calls

## 📊 Success Criteria

### Performance Benchmarks
- [ ] Initial load: Faster than sequential API calls
- [ ] Cache hits: < 100ms load time
- [ ] Background updates: No user-visible loading
- [ ] Memory usage: Stable, no leaks

### User Experience
- [ ] Smooth, responsive interface
- [ ] Real-time data updates
- [ ] Graceful error handling
- [ ] No breaking changes to existing functionality

### Technical Implementation
- [ ] Clean, maintainable code
- [ ] Proper TypeScript types
- [ ] No console errors or warnings
- [ ] Follows React Query best practices

## 🎉 Completion

When all items are checked:
- ✅ React Query conversion is successful
- ✅ Performance improvements are verified
- ✅ No regressions in existing functionality
- ✅ Ready for production deployment

## 📝 Notes Section

Use this space to record any issues found during testing:

```
Date: ___________
Tester: ___________

Issues Found:
- 
- 
- 

Performance Notes:
- 
- 
- 

Additional Observations:
- 
- 
- 
```
